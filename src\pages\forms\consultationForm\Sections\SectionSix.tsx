import React, { useState } from "react";
import View from "@/components/view";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import useForm from "@/utils/custom-hooks/use-form";
import SingleSelector from "@/components/SingleSelector";
import { Consultation } from "@/interfaces/consultation";
import { statusOptions } from "../consultationFormOptions";
import { useServiceCost } from "@/actions/calls/serviceCost";
import DynamicFormSection from "@/components/DynamicFormSection";
import { se } from "react-day-picker/locale";
import { addDynamicFieldSections } from "@/actions/slices/consultation/dynamicFieldSections";
// import dayjs from "dayjs";
// import Text from "@/components/text";
// import Text from "@/components/text";
// import Input from "@/components/input";
// import Select from "@/components/Select";
// import Button from "@/components/button";
// import { Card } from "@/components/ui/card";
// import Textarea from "@/components/Textarea";
// import { useOpd } from "@/actions/calls/opd";
// import { useTest } from "@/actions/calls/test";
// import SearchSelect from "@/components/SearchSelect";
// import TransferList from "@/components/TransferList";
// import SearchSelect from "@/components/SearchSelect";
// import MultiSelector from "@/components/MultiSelector";
// import { useMedicine } from "@/actions/calls/medicine";
// import { Appointment } from "@/interfaces/appointments";
// import TipTapTextEditor from "@/components/TipTapTexteditor";
// import MedicinesSection from "@/components/MedicinesSection";
// import MultiSelectWithDropDown from "@/components/MultiSelectWithDropDown";

interface SectionFourProps {
  // errorsTemperature: string;
  // errorsBp: string;
  // errorsPulse: string;
  // errorsCvs: string;
  // errorsRs: string;
  // errorsTest: string;
  // postExaminationData: any;
  // mainOnSetHandler: (name: string, value: any) => void;
}

const SectionFive: React.FC<SectionFourProps> = (
  {
    // errorsTemperature,
    // errorsBp,
    // errorsPulse,
    // errorsCvs,
    // errorsRs,
    // errorsTest,
    // postExaminationData,
    // mainOnSetHandler,
  }
) => {
  // const examinationDetails = useSelector(
  //   (state: RootState) => state.examinations.examinationDetails
  // );
  // const { values, handleChange } = useForm<Examination | null>(
  //   examinationDetails
  // );
  // const { medicineDropdownHandler } = useMedicine();

  const dispatch = useDispatch();

  const consultationDetail = useSelector(
    (state: RootState) => state.consultation.consultationDetailData?.vitals
  );

  // const medicineDropdownData = useSelector(
  //     (state: RootState) => state.medicines.medicineDropdownData
  //   )?.map((item: any) => ({
  //     id: item?.id,
  //     label: item?.medicine_name,
  //     value: item?.medicine_name,
  //   }));

  //   useEffect(() => {
  //       medicineDropdownHandler(() => {});
  //     }, []);

  // const testIds = testData?.split(",")?.map((item: any) => item.trim());
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => {
  //   return {
  //     id: item?.value,
  //     label: item?.label,
  //     value: item?.value,
  //   };
  // });
  // const testLabelMap = testObj?.filter((item: any) =>
  //   testIds?.includes(item?.value?.toString())
  // )?.map((item: any) => item?.label)?.join(",");
  // console.log("testLabelMap", testLabelMap);

  const { serviceCostDropdownHandler } = useServiceCost();
  const [serviceCostStatus, setServiceCostStatus] = useState<boolean>(false);

  const serviceCostDropdownData = useSelector(
    (state: RootState) => state.serviceCost.serviceCostDropdownData
  )?.map((data) => {
    return {
      id: data?.id,
      label: data?.service_name,
      value: data?.service_name + "#" + data?.cost,
    };
  });

  // const { values, handleChange, handleTipTapChange, onSetHandler } =
  //   useForm<Consultation | null>(consultationDetail);
  const { values, onSetHandler } = useForm<Consultation | null>(
    consultationDetail
  );

  return (
    <React.Fragment>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* consultatioin type  */}
        <View>
          <SingleSelector
            id="type"
            label="Consultation Type"
            name="type"
            value={values?.type || ""}
            placeholder="Select Consultation Type"
            onChange={(value) => {
              onSetHandler("type", value);
            }}
            options={[
              { label: "Proctology", value: "Proctology" },
              { label: "Non Proctology", value: "Non Proctology" },
              { label: "Allopathy", value: "Allopathy" },
            ]}
            required={true}
          />
        </View>

        {/* consultaton cost  */}
        <View>
          <SingleSelector
            id="cost"
            label="Consultation Cost"
            name="cost"
            // value={values?.cost || ""}
            placeholder="Select Consultation Cost"
            onChange={(value) => {
              onSetHandler("cost", value);
            }}
            options={[
              { label: "Free", value: "Free" },
              { label: "Paid", value: "Paid" },
            ]}
            required={true}
          />
        </View>
      </View>

      <View>
        <DynamicFormSection
          title="Addition Costs"
          itemLabelPrefix="Addition cost"
          addButtonText="Add Cost"
          onPressed={() => {
            if (!serviceCostStatus) {
              serviceCostDropdownHandler(() => {
                setServiceCostStatus(true);
              });
            }
          }}
          data={""}
          fieldConfigs={[
            {
              key: "Service",
              label: "Service#Cost",
              type: "custom-select",
              placeholder: "Select Service",
              required: true,
              options: serviceCostDropdownData,
              // options: [
              //   { label: "Test", value: "Test" },
              //   { label: "Medicine", value: "Medicine" },
              //   { label: "Surgery", value: "Surgery" },
              //   { label: "Consultation", value: "Consultation" },
              //   { label: "Admission", value: "Admission" },
              //   { label: "Discharge", value: "Discharge" },
              // ],
            },
            // {
            //   key: "Cost",
            //   label: "Cost",
            //   type: "number",
            //   placeholder: "Enter Cost",
            //   required: true,
            // },
          ]}
          onDataChange={(data) => {
            dispatch(addDynamicFieldSections(data));
            // onSetHandler("billing_details", data);
          }}
        />
      </View>

      <View>
        <SingleSelector
          id="status"
          label="Status"
          name="status"
          // error={errorsStatus}
          value={values?.status || ""}
          placeholder="Select Status"
          onChange={(value) => {
            onSetHandler("status", value);
          }}
          options={statusOptions}
        />
      </View>
    </React.Fragment>
  );
};
export default SectionFive;
