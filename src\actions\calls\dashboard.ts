import LaunchApi from "../api";
import { useDispatch } from "react-redux";
import { ApiCallback } from "@/interfaces/api";
import { DASHBOARD_URL } from "@/utils/urls/backend";
import { AuthPayload } from "@/interfaces/slices/auth";
import { dashboardDataSlice } from "../slices/dashboard";

const api = new LaunchApi();

export const useDashboard = () => {
  const dispatch = useDispatch();
  const getDashboardDataHandler = async (
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.get(
        DASHBOARD_URL,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(dashboardDataSlice(response));
            return callback(true, response.data);
          } else {
            callback(false, { success: false });
          }
        }
      );
    } catch (error) {
      callback(false, { success: false });
    }
  };

  return {
    getDashboardDataHandler,
  };
};
