import View from "@/components/view";
import Input from "@/components/input";
import Select from "@/components/Select";
import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import useForm from "@/utils/custom-hooks/use-form";
import { genderOptions, maritalStatusOptions } from "./patientFormOptions";
import { PatientInterface } from "@/interfaces/patients";
import { useAgeCalculate } from "@/utils/custom-hooks/use-age-calculate";

interface SectionOneProps {
  errorDOB: string;
  errorEmail: string;
  errorPhoneNo: string;
  errorsGender: string;
  errorLastName: string;
  errorFirstName: string;
  errorMaritalStatus: string;
  errorAge: string;
  errorAttendantWithPatientName: string;
  errorAttendantWithPatientPhoneNo: string;
}

const SectionOne: React.FC<SectionOneProps> = ({
  errorDOB,
  errorEmail,
  errorsGender,
  errorPhoneNo,
  errorAge,
  errorLastName,
  errorFirstName,
  errorMaritalStatus,
  errorAttendantWithPatientName,
  errorAttendantWithPatientPhoneNo,
}) => {
  const patientDetail = useSelector(
    (state: any) => state.patient.patientDetailData
  );

  const { userAge, calculateAge } = useAgeCalculate();
  const { values, handleChange, onSetHandler } =
    useForm<PatientInterface>(patientDetail);
  useEffect(() => {
    if (patientDetail?.dob) {
      onSetHandler("dob", patientDetail?.dob);
      calculateAge(patientDetail.dob);
    }
  }, [patientDetail?.dob]);
  return (
    <React.Fragment>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <View>
          <Input
            type="text"
            label="First Name"
            id="first_name"
            name="first_name"
            className={`w-full`}
            error={errorFirstName}
            onChange={handleChange}
            placeholder="First Name"
            value={values?.first_name}
            required={true}
          />
        </View>

        <View>
          <Input
            type="text"
            label="Last Name"
            id="last_name"
            name="last_name"
            className={`w-full`}
            error={errorLastName}
            onChange={handleChange}
            placeholder="Last Name"
            value={values?.last_name}
            required={true}
          />
        </View>
      </View>

      <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <View>
          <Input
            id="age"
            name="age"
            label="Age"
            required={true}
            error={errorAge}
            placeholder="Enter Age"
            readOnly={values?.dob ? true : false}
            value={
              values?.age
                ? values?.age + ""
                : !values?.dob && userAge
                ? ""
                : userAge
            }
            className="w-full bg-gray-100"
          />
        </View>

        <View>
          <Input
            type="tel"
            label="Phone No"
            id="phone_no"
            name="phone_no"
            error={errorPhoneNo}
            className={`w-full`}
            onChange={handleChange}
            value={values?.phone_no}
            placeholder="Phone Number"
            required={true}
          />
        </View>
      </View>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <View>
          <Select
            id="gender"
            label="Gender"
            name="gender"
            variant="default"
            className="w-full"
            placeholder="Gender"
            error={errorsGender}
            options={genderOptions}
            required={true}
          />
        </View>
        <View>
          <Select
            label="Marital Status"
            variant="default"
            className="w-full"
            id="marital_status"
            name="marital_status"
            required={true}
            error={errorMaritalStatus}
            options={maritalStatusOptions}
            value={values?.marital_status}
            placeholder="Select marital status"
            onChange={(e) => onSetHandler("marital_status", e.target.value)}
          />
        </View>
      </View>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <View>
          <Input
            label="Date of Birth"
            id="dob"
            name="dob"
            type="date"
            error={errorDOB}
            className={`w-full`}
            max={new Date().toISOString().split("T")[0]}
            value={
              values?.dob instanceof Date
                ? values.dob.toISOString().split("T")[0]
                : values?.dob || ""
            }
            onChange={(e) => {
              onSetHandler("dob", e.currentTarget.value);
              if (values?.dob) {
                calculateAge(new Date(values?.dob).toISOString().split("T")[0]);
              }
            }}
            onBlur={(e) => {
              onSetHandler("dob", e.currentTarget.value);
              if (values?.dob) {
                calculateAge(new Date(values?.dob).toISOString().split("T")[0]);
              }
            }}
            onKeyUp={(e) => {
              onSetHandler("dob", e.currentTarget.value);
              if (values?.dob) {
                calculateAge(new Date(values?.dob).toISOString().split("T")[0]);
              }
            }}
            // required={true}
          />
        </View>

        <View>
          <Input
            id="email"
            label="Email"
            type="email"
            name="email"
            // required={true}
            error={errorEmail}
            placeholder="Email"
            className={`w-full`}
            value={values?.email}
            onChange={handleChange}
          />
        </View>

        <View>
          <Input
            label="Attendant With Patient Name"
            type="text"
            id="attendant_with_patient_name"
            className="w-full"
            name="attendant_with_patient_name"
            placeholder="Attendant With Patient Name"
            error={errorAttendantWithPatientName}
            onChange={handleChange}
            value={values?.attendant_with_patient_name}
          />
        </View>

        <View>
          <Input
            label="Attendant With Patient Phone No"
            type="text"
            className="w-full"
            onChange={handleChange}
            id="attendant_with_patient_phone_no"
            name="attendant_with_patient_phone_no"
            placeholder="Attendant With Patient Phone No"
            error={errorAttendantWithPatientPhoneNo}
            value={values?.attendant_with_patient_phone_no}
          />
        </View>
      </View>
    </React.Fragment>
  );
};

export default SectionOne;
