import View from "@/components/view";
import Input from "@/components/input";
import Select from "@/components/Select";
import React, { useEffect } from "react";
import { useSelector } from "react-redux";
import useForm from "@/utils/custom-hooks/use-form";
import { genderOptions, maritalStatusOptions } from "./patientFormOptions";
import { PatientInterface } from "@/interfaces/patients";
import { useAgeCalculate } from "@/utils/custom-hooks/use-age-calculate";
import { Ids } from "@/interfaces";
import Text from "@/components/text";
import Upload from "@/components/Upload";
import SingleSelector from "@/components/SingleSelector";

interface SectionOneProps {
  errorDOB: string;
  errorEmail: string;
  errorPhoneNo: string;
  errorsGender: string;
  errorLastName: string;
  errorFirstName: string;
  errorMaritalStatus: string;
  errorAge: string;
  setImage: (name: string, value: any) => void;
  errorAttendantWithPatientName: string;
  errorAttendantWithPatientPhoneNo: string;
  formType: "add" | "edit";
}

const SectionOne: React.FC<SectionOneProps> = ({
  errorDOB,
  errorEmail,
  errorsGender,
  errorPhoneNo,
  errorAge,
  errorLastName,
  errorFirstName,
  errorMaritalStatus,
  errorAttendantWithPatientName,
  errorAttendantWithPatientPhoneNo,
  formType,
  setImage,
}) => {
  const patientDetail = useSelector(
    (state: any) => state.patient.patientDetailData
  );
  const patientDetailData = {
    ...patientDetail,
    id_edited: patientDetail?.id_number_masked,
  };

  const { userAge, calculateAge } = useAgeCalculate();
  const { values, handleChange, onSetHandler } =
    useForm<PatientInterface>(patientDetailData);
  useEffect(() => {
    if (patientDetail?.dob) {
      onSetHandler("dob", patientDetail?.dob);
      calculateAge(patientDetail.dob);
    }
  }, [patientDetail?.dob]);
  return (
    <React.Fragment>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <View>
          <Input
            type="text"
            label="First Name"
            id="first_name"
            name="first_name"
            className={`w-full`}
            error={errorFirstName}
            onChange={handleChange}
            placeholder="First Name"
            value={values?.first_name}
            required={true}
          />
        </View>

        <View>
          <Input
            type="text"
            label="Last Name"
            id="last_name"
            name="last_name"
            className={`w-full`}
            error={errorLastName}
            onChange={handleChange}
            placeholder="Last Name"
            value={values?.last_name}
            required={true}
          />
        </View>
      </View>

      <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <View>
          <Input
            id="age"
            name="age"
            label="Age"
            required={true}
            error={errorAge}
            placeholder="Enter Age"
            onChange={handleChange}
            readOnly={values?.dob ? true : false}
            value={
              values?.age
                ? values?.age + ""
                : !values?.dob && userAge
                ? ""
                : userAge
            }
            className="w-full"
          />
        </View>

        <View>
          <Input
            type="tel"
            label="Phone No"
            id="phone_no"
            name="phone_no"
            error={errorPhoneNo}
            className={`w-full`}
            onChange={handleChange}
            value={values?.phone_no}
            placeholder="Phone Number"
            required={true}
          />
        </View>
      </View>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <View>
          {/* <Select
            id="gender"
            label="Gender"
            name="gender"
            variant="default"
            className="w-full"
            placeholder="Gender"
            error={errorsGender}
            options={genderOptions}
            required={true}
          /> */}
          <SingleSelector
            id="gender"
            label="Gender"
            name="gender"
            error={errorsGender}
            value={values?.gender || ""}
            placeholder="Select Gender"
            onChange={(value) => {
              onSetHandler("gender", value);
            }}
            options={genderOptions}
            closeOnSelect={true}
            required={true}
          />
        </View>
        <View>
          {/* <Select
            label="Marital Status"
            variant="default"
            className="w-full"
            id="marital_status"
            name="marital_status"
            required={true}
            error={errorMaritalStatus}
            options={maritalStatusOptions}
            value={values?.marital_status}
            placeholder="Select marital status"
            onChange={(e) => onSetHandler("marital_status", e.target.value)}
          /> */}
          <SingleSelector
            id="marital_status"
            label="Marital Status"
            name="marital_status"
            error={errorMaritalStatus}
            value={values?.marital_status || ""}
            placeholder="Select Marital Status"
            onChange={(value) => {
              onSetHandler("marital_status", value);
            }}
            options={maritalStatusOptions}
            closeOnSelect={true}
            required={true}
          />
        </View>
      </View>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <View>
          <Input
            label="Date of Birth"
            id="dob"
            name="dob"
            type="date"
            error={errorDOB}
            className={`w-full`}
            max={new Date().toISOString().split("T")[0]}
            value={
              values?.dob instanceof Date
                ? values.dob.toISOString().split("T")[0]
                : values?.dob || ""
            }
            onChange={(e) => {
              onSetHandler("dob", e.currentTarget.value);
              if (values?.dob) {
                calculateAge(new Date(values?.dob).toISOString().split("T")[0]);
              }
            }}
            onBlur={(e) => {
              onSetHandler("dob", e.currentTarget.value);
              if (values?.dob) {
                calculateAge(new Date(values?.dob).toISOString().split("T")[0]);
              }
            }}
            onKeyUp={(e) => {
              onSetHandler("dob", e.currentTarget.value);
              if (values?.dob) {
                calculateAge(new Date(values?.dob).toISOString().split("T")[0]);
              }
            }}
            // required={true}
          />
        </View>

        <View>
          <Input
            id="email"
            label="Email"
            type="email"
            name="email"
            // required={true}
            error={errorEmail}
            placeholder="Email"
            className={`w-full`}
            value={values?.email}
            onChange={handleChange}
          />
        </View>

        <View>
          <Input
            label="Attendant With Patient Name"
            type="text"
            id="attendant_with_patient_name"
            className="w-full"
            name="attendant_with_patient_name"
            placeholder="Attendant With Patient Name"
            error={errorAttendantWithPatientName}
            onChange={handleChange}
            value={values?.attendant_with_patient_name}
          />
        </View>

        <View>
          <Input
            label="Attendant With Patient Phone No"
            type="text"
            className="w-full"
            onChange={handleChange}
            id="attendant_with_patient_phone_no"
            name="attendant_with_patient_phone_no"
            placeholder="Attendant With Patient Phone No"
            error={errorAttendantWithPatientPhoneNo}
            value={values?.attendant_with_patient_phone_no}
          />
        </View>

        <View className="space-y-4">
          <Select
            id="id_type"
            label="Identifications"
            name="id_type"
            // error={errorsIds}
            value={values?.id_type || ""}
            placeholder="Select ID"
            onChange={(e) => {
              onSetHandler("id_type", e?.currentTarget?.value);
            }}
            options={[
              { value: Ids.ADHAR, label: "Aadhar" },
              { value: Ids.PASSPORT, label: "Passport" },
              { value: Ids.VOTER_ID, label: "Voter ID" },
              { value: Ids.DRIVING_LICENSE, label: "Driving License" },
              { value: Ids.RATION_CARD, label: "Ration Card" },
              { value: "", label: "None" },
            ]}
          />

          {values?.id_type ? (
            <>
              <Input
                name="id_value"
                label={`Enter ${
                  values?.id_type?.charAt(0)?.toUpperCase() +
                  values?.id_type?.slice(1)
                } Number`}
                onChange={(e) => {
                  if (formType === "edit") {
                    setImage("id_edited", true);
                  } else {
                    setImage("id_edited", false);
                  }
                  onSetHandler("id_value", e.target.value);
                }}
                // onChange={handleChange}
                // error={errorsIdValue}
                value={
                  formType === "edit"
                    ? values?.id_value
                      ? values?.id_value || ""
                      : values?.id_number_masked || ""
                    : values?.id_value || ""
                }
                // value={values?.id_value || ""}
                placeholder="Enter ID Number"
                required={true}
              />

              <View className="flex items-center justify-center space-x-2">
                <View>
                  <Input
                    type="checkbox"
                    name="consent"
                    checked={!!values?.consent}
                    onChange={(e) => {
                      onSetHandler("consent", e.target.checked);
                    }}
                    value={values?.consent}
                  />
                </View>
                <Text className="font-sm w-full">
                  I consent to the storage of my ID for address verification.
                </Text>
              </View>
              {/* {errorConsent ? (
                <Text className="text-sm text-red-500">{errorConsent}</Text>
              ) : null} */}
            </>
          ) : null}
        </View>
        {values?.id_type ? (
          <View className="col-span-2">
            <Upload
              label="Upload ID Proof (Adhar Card, Passport, Voter ID, etc.)"
              name="image"
              accept=".pdf,.doc,.docx,.txt,.jpg,.png"
              maxSize={1024 * 1024 * 2}
              multiple={false}
              maxCount={1}
              // required={true}
              // error={errorsImage}
              existingFiles={
                typeof values?.image === "string"
                  ? values?.image
                  : Array.isArray(values?.image) && values?.image.length > 0
                  ? values?.image
                      .filter((item) => typeof item === "string")
                      .join(",")
                  : ""
              }
              onChange={(fileList: any) => {
                const file =
                  fileList?.map((item: any) => {
                    if (item.isExisting) {
                      return item.url;
                    } else {
                      return item.file;
                    }
                  }) || [];

                setImage("image", file);
              }}
            />
          </View>
        ) : null}
      </View>
    </React.Fragment>
  );
};

export default SectionOne;
