import { useInvoice } from "@/actions/calls/invoice";
import { RootState } from "@/actions/store";
import Button from "@/components/button";
import Input from "@/components/input";
import Modal from "@/components/Modal";
import PaginationComponent from "@/components/Pagination";
import Select from "@/components/Select";
import DataSort, { SortOption } from "@/components/SortData";
import Text from "@/components/text";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import SearchBar from "@/components/ui/search-bar";
import View from "@/components/view";
import { handleSortChange } from "@/utils/helperFunctions";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import { INVOICE_DETAIL_URL, INVOICE_URL } from "@/utils/urls/frontend";
import dayjs from "dayjs";

import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { useSearchParams } from "react-router-dom";
import Filter from "../filter";

const InvoicePage: React.FC<{}> = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [deleteId, setDeleteId] = useState<null | string>(null);
  const { getInvoiceListHandler, cleanUp } = useInvoice();
  const invoiceData = useSelector(
    (state: RootState) => state?.invoice?.invoiceListData
  );
  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );
  useEffect(() => {
    if (searchParams?.has("currentPage")) {
      getInvoiceListHandler(
        searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null,
        filterData
      );
    }
    return () => {
      cleanUp();
    };
  }, [
    filterData,
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
    searchParams?.get("currentPage"),
  ]);

  const modalCloseHandler = () => {
    setDeleteId(null);
  };

  //   const handleDeletePatient = () => {
  //     if (deleteId) {
  //       deleteYogaAsanaHandler(deleteId, (success: boolean) => {
  //         if (success) {
  //           modalCloseHandler();
  //           yogaAsanaListHandler(searchParams?.get("currentPage") ?? 1, () => {});
  //         }
  //       });
  //     }
  //   };
  const sortOptions: SortOption[] = [
    { label: "Invoice Number (A-Z)", value: "invoice_number", order: "asc" },
    { label: "Invoice Number (Z-A)", value: "invoice_number", order: "desc" },
    { label: "Appointment Number (A-Z)", value: "appointment_number", order: "asc" },
    { label: "Appointment Number (Z-A)", value: "appointment_number", order: "desc" },
    { label: "Patient Number (A-Z)", value: "patient_number", order: "asc" },
    { label: "Patient Number (Z-A)", value: "patient_number", order: "desc" },
    { label: "Doctor Name (A-Z)", value: "doctor_name", order: "asc" },
    { label: "Doctor Name (Z-A)", value: "doctor_name", order: "desc" },
    { label: "Next Visit Date (A-Z)", value: "next_visit_date", order: "asc" },
    { label: "Next Visit Date (Z-A)", value: "next_visit_date", order: "desc" },
  ];
  const [activeSort, setActiveSort] = useState<SortOption | null>(null);

  return (
    <React.Fragment>
      <Modal
        title="Invoice Delete"
        isOpen={deleteId ? true : false}
        onClose={modalCloseHandler}
        description="Are you sure you want to delete this data? This action cannot be undone and will permanently remove the data from the system."
      >
        <View className="flex justify-end gap-2">
          <Button
            variant="outline"
            className="text-black"
            onPress={modalCloseHandler}
          >
            Cancel
          </Button>
          <Button variant="danger">Delete</Button>
        </View>
      </Modal>
      <View className="mb-6">
        <Text
          as="h1"
          weight="font-semibold"
          className="text-2xl font-bold text-text-DEFAULT mb-1"
        >
          Invoices
        </Text>
        <Text as="p" className="text-text-light">
          Manage Invoices
        </Text>
      </View>

      <Card className="overflow-hidden">
        <DynamicTable
          tableHeaders={[
            "Invoice Number",
            "Appointment Number",
            "Patient Number",
            "Doctor Name",
            "Status",
            "Payment Status",
            "Next Visit Date",
          ]}
          tableData={invoiceData?.data?.data?.map((data: any) => [
            <Link
              to={`${INVOICE_URL}${INVOICE_DETAIL_URL}/${data.id}`}
              className="font-medium text-text-DEFAULT hover:text-secondary hover:underline"
            >
              {data?.invoice_number?.invoice_number ?? "NA"}
            </Link>,
            data?.appointment_number || "N/A",
            // data?.appointment_number,
            data?.patient_number,
            data?.doctor_name,
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(data?.status)}
            >
              {data?.status}
            </Text>,
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(data?.payment_status)}
            >
              {data?.payment_status}
            </Text>,
            data?.next_visit_date
              ? dayjs(data?.next_visit_date).format("DD-MM-YYYY")
              : "NA",
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(val) =>
                  setSearchParams({
                    ...Object.fromEntries(searchParams),
                    search: val,
                    currentPage: "1",
                  })
                }
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                activeSort={activeSort ?? undefined}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
              />
            ),
            filter: (
              <Filter
                title="Invoice Filter"
                onResetFilter={() => {
                  setFilterData(null);
                }}
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="patient_number" placeholder="Patient Number" />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="appointment_number"
                      placeholder="Appointment Number"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="doctor_name" placeholder="Doctor Name" />
                  </View>,
                  <View className="w-full my-4">
                    <Select
                      label="Status"
                      name="status"
                      // value={paymentStatus}
                      // onChange={(e) => {
                      //   setPaymentStatus(e.target.value);
                      //   // onSetHandler("payment_status", e.target.value)
                      // }}
                      placeholder="Select Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                      // error={errorsPaymentStatus}
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Select
                      label="Payment Status"
                      name="payment_status"
                      // value={paymentStatus}
                      // onChange={(e) => {
                      //   setPaymentStatus(e.target.value);
                      //   // onSetHandler("payment_status", e.target.value)
                      // }}
                      placeholder="Select Payment Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                      // error={errorsPaymentStatus}
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      type="date"
                      name="next_visit_date"
                      placeholder="Next Visit Date"
                      onFocus={(e) => (e.target.type = "date")}
                    />
                  </View>,
                ]}
              />
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={invoiceData?.data?.current_page}
                last_page={invoiceData?.data?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
      </Card>
    </React.Fragment>
  );
};
export default InvoicePage;
