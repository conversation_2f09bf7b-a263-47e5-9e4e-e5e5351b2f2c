import React, { useState, useRef, useCallback } from 'react';
import { Camera, RotateCcw, X, Settings, Smartphone, Monitor } from 'lucide-react';
import Upload from './Upload';
import { ExtendedFileItem } from '@/interfaces/components/input/uploadProps';

// Custom Webcam component
interface CaptureProps {
  audio?: boolean;
  screenshotFormat?: string;
  videoConstraints?: any;
  className?: string;
  mirrored?: boolean;
  screenshotQuality?: number;
}

const Capture = React.forwardRef<any, CaptureProps>(({
  audio = false,
  screenshotFormat = "image/jpeg",
  videoConstraints = {},
  className = "",
  mirrored = true,
  ...props
}, ref) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);

  React.useEffect(() => {
    const startCamera = async () => {
      try {
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          video: videoConstraints.facingMode ? 
            { facingMode: videoConstraints.facingMode, ...videoConstraints } : 
            { ...videoConstraints },
          audio
        });
        setStream(mediaStream);
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;
        }
      } catch (err) {
        console.error('Camera access error:', err);
      }
    };

    startCamera();

    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, [videoConstraints, audio]);

  React.useImperativeHandle(ref, () => ({
    getScreenshot: () => {
      if (!videoRef.current) return null;

      const video = videoRef.current as HTMLVideoElement;

      // Simple approach: Use HTML5 Canvas without context manipulation
      const canvas = document.createElement('canvas');
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Handle mirroring by temporarily applying CSS transform
      const originalTransform = video.style.transform;

      if (mirrored) {
        // Apply mirror transform to video element
        video.style.transform = 'scaleX(-1)';

        // Wait for transform to take effect, then capture
        setTimeout(() => {
          const context = canvas.getContext('2d');
          if (context) {
            context.drawImage(video, 0, 0);
          }

          // Restore original transform
          video.style.transform = originalTransform;
        }, 10);
      } else {
        const context = canvas.getContext('2d');
        if (context) {
          context.drawImage(video, 0, 0);
        }
      }

      return canvas.toDataURL(screenshotFormat);
    }
  }));

  return (
    <video
      ref={videoRef}
      autoPlay
      playsInline
      muted
      className={className}
      style={{ transform: mirrored ? 'scaleX(-1)' : 'none' }}
    />
  );
});

interface WebcamCaptureProps {
  name?: string;
  onChange?: (fileList: ExtendedFileItem[]) => void;
  value?: string | null;
  className?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  quality?: number;
  width?: number;
  height?: number;
  // Upload component props
  accept?: string;
  multiple?: boolean;
  maxSize?: number;
  minSize?: number;
  maxCount?: number;
  maxFiles?: number;
  maxFileNameLength?: number;
  fullWidth?: boolean;
  label?: string;
  error?: string;
  helperText?: string;
  showFileList?: boolean;
  showPreview?: boolean;
  browseText?: string;
  existingFiles?: string[] | string | null;
  onUpload?: (files: File[]) => Promise<void>;
}

const WebcamCapture: React.FC<WebcamCaptureProps> = ({
  name = 'webcam-image',
  onChange,
  value,
  className = '',
  placeholder = 'Click to capture image',
  required = false,
  disabled = false,
  quality = 0.92,
  width = 1280,
  height = 720,
  // Upload props
  accept = 'image/*',
  multiple = true,
  maxSize,
  minSize,
  maxCount,
  maxFiles,
  maxFileNameLength,
  fullWidth = false,
  label,
  error,
  helperText,
  showFileList = true,
  showPreview = true,
  browseText = 'Upload Files',
  existingFiles,
  onUpload
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [facingMode, setFacingMode] = useState('user');
  const [showSettings, setShowSettings] = useState(false);
  const [resolution, setResolution] = useState({ width, height });
  const [fileList, setFileList] = useState<ExtendedFileItem[]>([]);

  const webcamRef = useRef<any>(null);

  const resolutionOptions = [
    { label: 'VGA (640x480)', width: 640, height: 480 },
    { label: 'HD (1280x720)', width: 1280, height: 720 },
    { label: 'Full HD (1920x1080)', width: 1920, height: 1080 },
    { label: '4K (3840x2160)', width: 3840, height: 2160 }
  ];

  const videoConstraints = {
    width: resolution.width,
    height: resolution.height,
    facingMode: facingMode
  };

  const generateUniqueId = () => {
    return `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const captureImage = useCallback(() => {
    const imageSrc = webcamRef.current?.getScreenshot();
    if (!imageSrc) return;

    // Convert data URL to File object
    fetch(imageSrc)
      .then(res => res.blob())
      .then(blob => {
        const file = new File([blob], `webcam-capture-${Date.now()}.jpg`, {
          type: 'image/jpeg'
        });

        // Create new file item
        const newFileItem: ExtendedFileItem = {
          file,
          id: generateUniqueId(),
          name: file.name,
          size: file.size,
          type: file.type,
          isExisting: false
        };

        // Add to file list
        const updatedFileList = [...fileList, newFileItem];
        setFileList(updatedFileList);

        // Call onChange callback if provided
        if (onChange) {
          onChange(updatedFileList);
        }

        // Call onUpload if provided
        if (onUpload) {
          onUpload([file]).catch((error) => {
            console.error("Upload error:", error);
          });
        }
      });

    setIsOpen(false);
  }, [fileList, onChange, onUpload]);

  const handleUploadChange = (updatedFileList: ExtendedFileItem[]) => {
    setFileList(updatedFileList);
    if (onChange) {
      onChange(updatedFileList);
    }
  };

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setShowSettings(false);
  }, []);

  const switchCamera = () => {
    setFacingMode(prev => prev === 'user' ? 'environment' : 'user');
  };

  return (
    <div className={`relative ${className}`}>
      {/* Upload Component */}
      <Upload
        name={name}
        label={label}
        accept={accept}
        multiple={multiple}
        disabled={disabled}
        maxSize={maxSize}
        minSize={minSize}
        maxCount={maxCount}
        maxFiles={maxFiles}
        maxFileNameLength={maxFileNameLength}
        fullWidth={fullWidth}
        required={required}
        error={error}
        helperText={helperText}
        showFileList={showFileList}
        showPreview={showPreview}
        browseText={browseText}
        existingFiles={existingFiles}
        fileList={fileList}
        onChange={handleUploadChange}
        onUpload={onUpload}
        className="mb-4"
      />

      {/* Camera Capture Button */}
      <div className="flex gap-2">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(true)}
          disabled={disabled}
          className={`
            flex items-center gap-2 px-4 py-2 rounded-lg border-2 border-dashed transition-all duration-300
            ${disabled
              ? 'opacity-50 cursor-not-allowed border-gray-300 bg-gray-50'
              : 'border-blue-400 bg-blue-50 hover:border-blue-500 hover:bg-blue-100 cursor-pointer'
            }
          `}
        >
          <Camera className="w-5 h-5 text-blue-600" />
          <span className="text-sm font-medium text-blue-700">
            {placeholder}
          </span>
        </button>
      </div>

      {/* Camera Modal */}
      {isOpen && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-3xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-hidden">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-purple-50">
              <div>
                <h3 className="text-xl font-bold text-gray-900">Camera Capture</h3>
                <p className="text-sm text-gray-600 mt-1">Position yourself and click capture when ready</p>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className={`p-3 rounded-xl transition-all duration-200 ${
                    showSettings 
                      ? 'bg-blue-100 text-blue-600' 
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}
                  title="Settings"
                >
                  <Settings className="w-5 h-5" />
                </button>
                <button
                  onClick={closeModal}
                  className="p-3 rounded-xl hover:bg-red-50 text-gray-600 hover:text-red-600 transition-all duration-200"
                  title="Close"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Settings Panel */}
            {showSettings && (
              <div className="p-6 bg-gradient-to-r from-gray-50 to-blue-50 border-b border-gray-100">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      <Smartphone className="inline w-4 h-4 mr-2" />
                      Camera Source
                    </label>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => setFacingMode('user')}
                        className={`p-3 rounded-xl border-2 transition-all duration-200 text-sm font-medium ${
                          facingMode === 'user'
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 hover:border-gray-300 text-gray-600'
                        }`}
                      >
                        Front Camera
                      </button>
                      <button
                        onClick={() => setFacingMode('environment')}
                        className={`p-3 rounded-xl border-2 transition-all duration-200 text-sm font-medium ${
                          facingMode === 'environment'
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 hover:border-gray-300 text-gray-600'
                        }`}
                      >
                        Back Camera
                      </button>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-3">
                      <Monitor className="inline w-4 h-4 mr-2" />
                      Resolution
                    </label>
                    <select
                      value={`${resolution.width}x${resolution.height}`}
                      onChange={(e) => {
                        const [width, height] = e.target.value.split('x').map(Number);
                        setResolution({ width, height });
                      }}
                      className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all bg-white text-gray-700 font-medium"
                    >
                      {resolutionOptions.map((res) => (
                        <option key={res.label} value={`${res.width}x${res.height}`}>
                          {res.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
            )}

            {/* Video Feed */}
            <div className="relative bg-gradient-to-br from-gray-900 to-black flex items-center justify-center min-h-[400px] p-4">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-black">
                <Capture
                  ref={webcamRef}
                  audio={false}
                  screenshotFormat="image/jpeg"
                  screenshotQuality={quality}
                  videoConstraints={videoConstraints}
                  className="max-w-full max-h-[60vh] object-contain"
                  mirrored={facingMode === 'user'}
                />
                
                {/* Camera Controls Overlay */}
                <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
                  <div className="flex items-center gap-2 bg-black/50 backdrop-blur-sm text-white px-3 py-2 rounded-full text-sm font-medium">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    LIVE
                  </div>
                  <button
                    onClick={switchCamera}
                    className="p-3 bg-black/50 backdrop-blur-sm text-white rounded-full hover:bg-black/70 transition-all duration-200"
                    title="Switch camera"
                  >
                    <RotateCcw className="w-5 h-5" />
                  </button>
                </div>

                {/* Center focus indicator */}
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="w-32 h-32 border-2 border-white/30 rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Controls */}
            <div className="p-6 bg-gradient-to-r from-gray-50 to-blue-50 flex items-center justify-center gap-4">
              <button
                onClick={closeModal}
                className="px-8 py-3 bg-gray-200 text-gray-700 rounded-xl hover:bg-gray-300 transition-all duration-200 font-semibold border-2 border-transparent hover:border-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={captureImage}
                className="px-10 py-3 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 text-white rounded-xl hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3"
              >
                <Camera className="w-5 h-5" />
                Capture Photo
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Export the WebcamCapture component as default
export default WebcamCapture;

// Also export the Capture component if needed
export { Capture };

