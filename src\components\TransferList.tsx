import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Search, X, Check, Plus, Users, RotateCcw } from 'lucide-react';
import View from './view';
import Text from './text';
import Input from './input';
import Button from './button';
import SearchBar from './ui/search-bar';

// Generic item interface for reusable component
interface TransferListItem {
  id: string | number;
  name: string;
  label?: string; // For backward compatibility
  description?: string; // Secondary text
  subtitle?: string; // Additional info
  status?: string; // Any status value
  badge?: string; // Status badge text
  isCustom?: boolean;
  disabled?: boolean;
  [key: string]: any; // Allow any additional properties
}

interface TransferListProps {
  sourceData?: TransferListItem[];
  selectedItems?: TransferListItem[];
  onSelectionChange?: (items: TransferListItem[]) => void;
  placeholder?: string;
  sourceTitle?: string;
  selectedTitle?: string;
  height?: string;
  searchable?: boolean;
  disabled?: boolean;
  showCount?: boolean;
  allowSelectAll?: boolean;
  allowCustomValues?: boolean;
  customValuePlaceholder?: string;
  className?: string;
  label?: string;
  required?: boolean;
  error?: string;
  maxSelections?: number;
  variant?: 'default' | 'compact' | 'detailed';
}

const TransferList: React.FC<TransferListProps> = ({
  sourceData = [],
  selectedItems = [],
  onSelectionChange = () => {},
  placeholder = "Search items...",
  sourceTitle = "Available Items",
  selectedTitle = "Selected Items",
  height = "200px",
  searchable = true,
  disabled = false,
  showCount = true,
  allowSelectAll = true,
  allowCustomValues = false,
  customValuePlaceholder = "Add custom item...",
  className = "",
  label,
  required = false,
  error,
  maxSelections,
  variant = 'default',
  ...props
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSearchTerm, setSelectedSearchTerm] = useState('');
  const [selectedIds, setSelectedIds] = useState(new Set(selectedItems.map(item => item.id)));
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [activeList, setActiveList] = useState('source');
  const [customValue, setCustomValue] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);
  
  const sourceListRef = useRef(null);
  const selectedListRef = useRef(null);
  const searchInputRef = useRef(null);
  const selectedSearchInputRef = useRef(null);
  const customInputRef = useRef(null);

  // Filter items based on search term
  const filteredSourceData = sourceData.filter(item => {
    if (selectedIds.has(item.id)) return false;

    const searchLower = searchTerm.toLowerCase();
    return (
      item.name?.toLowerCase().includes(searchLower) ||
      item.label?.toLowerCase().includes(searchLower) ||
      item.description?.toLowerCase().includes(searchLower) ||
      item.subtitle?.toLowerCase().includes(searchLower) ||
      item.status?.toLowerCase().includes(searchLower) ||
      // Search through any additional string properties
      Object.values(item).some(value =>
        typeof value === 'string' && value.toLowerCase().includes(searchLower)
      )
    );
  });

  const filteredSelectedData = selectedItems.filter(item => {
    const searchLower = selectedSearchTerm.toLowerCase();
    return (
      item.name?.toLowerCase().includes(searchLower) ||
      item.label?.toLowerCase().includes(searchLower) ||
      item.description?.toLowerCase().includes(searchLower) ||
      item.subtitle?.toLowerCase().includes(searchLower) ||
      item.status?.toLowerCase().includes(searchLower) ||
      // Search through any additional string properties
      Object.values(item).some(value =>
        typeof value === 'string' && value.toLowerCase().includes(searchLower)
      )
    );
  });

  // Update selected items when prop changes
  useEffect(() => {
    setSelectedIds(new Set(selectedItems.map(item => item.id)));
  }, [selectedItems]);

  // Handle item selection
  const handleItemSelect = useCallback((item: TransferListItem) => {
    if (disabled) return;

    const newSelectedIds = new Set(selectedIds);
    const newSelectedItems = [...selectedItems];

    if (selectedIds.has(item.id)) {
      newSelectedIds.delete(item.id);
      const index = newSelectedItems.findIndex(selected => selected.id === item.id);
      if (index > -1) {
        newSelectedItems.splice(index, 1);
      }
    } else {
      // Check max selections limit
      if (maxSelections && newSelectedItems.length >= maxSelections) {
        return;
      }
      newSelectedIds.add(item.id);
      newSelectedItems.push(item);
    }

    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedItems);
  }, [selectedIds, selectedItems, onSelectionChange, disabled, maxSelections]);

  // Handle custom value addition
  const handleAddCustomValue = useCallback(() => {
    if (!customValue.trim() || disabled) return;
    
    const newItem = {
      id: Date.now() + Math.random(),
      name: customValue.trim(),
      label: customValue.trim(),
      isCustom: true
    };
    
    const newSelectedItems = [...selectedItems, newItem];
    const newSelectedIds = new Set([...selectedIds, newItem.id]);
    
    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedItems);
    setCustomValue('');
    setShowCustomInput(false);
  }, [customValue, selectedItems, selectedIds, onSelectionChange, disabled]);

  // Handle select all
  const handleSelectAll = useCallback(() => {
    if (disabled) return;

    const newSelectedItems = [...selectedItems];
    const newSelectedIds = new Set(selectedIds);

    filteredSourceData.forEach(item => {
      if (!selectedIds.has(item.id)) {
        // Check max selections limit
        if (maxSelections && newSelectedItems.length >= maxSelections) {
          return;
        }
        newSelectedIds.add(item.id);
        newSelectedItems.push(item);
      }
    });

    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedItems);
  }, [filteredSourceData, selectedIds, selectedItems, onSelectionChange, disabled, maxSelections]);

  // Handle deselect all
  const handleDeselectAll = useCallback(() => {
    if (disabled) return;

    const newSelectedItems = [...selectedItems];
    const newSelectedIds = new Set(selectedIds);

    filteredSelectedData.forEach(item => {
      if (selectedIds.has(item.id)) {
        newSelectedIds.delete(item.id);
        const index = newSelectedItems.findIndex(selected => selected.id === item.id);
        if (index > -1) {
          newSelectedItems.splice(index, 1);
        }
      }
    });

    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedItems);
  }, [filteredSelectedData, selectedIds, selectedItems, onSelectionChange, disabled]);

  // Keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent, listType: 'source' | 'selected') => {
    const currentList = listType === 'source' ? filteredSourceData : filteredSelectedData;
    const maxIndex = currentList.length - 1;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setActiveList(listType);
        setFocusedIndex(prev => Math.min(prev + 1, maxIndex));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setActiveList(listType);
        setFocusedIndex(prev => Math.max(prev - 1, 0));
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < currentList.length) {
          handleItemSelect(currentList[focusedIndex]);
        }
        break;
      case 'Escape':
        setFocusedIndex(-1);
        setShowCustomInput(false);
        break;
    }
  }, [filteredSourceData, filteredSelectedData, focusedIndex, handleItemSelect]);

  // Get icon for item type
  const getItemIcon = (item: TransferListItem) => {
    const iconProps = { size: 16, className: "text-slate-500 shrink-0" };
    if (item.isCustom) return <Plus {...iconProps} />;
    return <Users {...iconProps} />;
  };

  // Render list item
  const renderListItem = (item: TransferListItem, index: number, isSelected: boolean, listType: 'source' | 'selected') => {
    const isActive = activeList === listType && focusedIndex === index;
    
    return (
      <View
        key={item.id}
        className={`
          group flex items-start gap-3 p-3 rounded-lg cursor-pointer
          transition-all duration-200 ease-in-out
          ${isSelected
            ? 'bg-primary-50 dark:bg-card border border-primary-200 dark:border-border'
            : 'border border-transparent hover:bg-slate-50 dark:hover:bg-card'
          }
          ${isActive ? 'ring-1 ring-primary-500 ring-opacity-50' : ''}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onClick={() => !disabled && handleItemSelect(item)}
        onMouseEnter={() => setFocusedIndex(index)}
        role="option"
        aria-selected={isSelected}
        tabIndex={isActive ? 0 : -1}
      >
        {/* Checkbox */}
        <View className={`
          w-4 h-4 rounded border-2 transition-all duration-200 flex items-center justify-center shrink-0 mt-0.5
          ${isSelected ? 'border-primary-500 bg-primary-500' : 'border-slate-300 dark:border-border bg-white dark:bg-background'}
        `}>
          {isSelected && (
            <Check className="w-2.5 h-2.5 text-white" />
          )}
        </View>

        {/* Content */}
        <View className="flex-1 min-w-0 overflow-hidden">
          {/* Main content */}
          <View className="flex flex-col gap-1 mb-2">
            <Text className={`font-medium text-sm leading-tight ${
              isSelected ? 'text-primary-700 dark:text-primary-300' : 'text-slate-900 dark:text-slate-100'
            }`}
            style={{ 
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              whiteSpace: 'normal'
            }}>
              {item.name || item.label}
            </Text>
            
            {/* Badges */}
            <View className="flex flex-wrap items-center gap-1">
              {item.badge && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary-100 dark:bg-primary-900/30 text-primary-800 dark:text-primary-200"
                style={{ 
                  wordBreak: 'break-word',
                  overflowWrap: 'break-word'
                }}>
                  {item.badge}
                </span>
              )}
              {item.isCustom && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200">
                  Custom
                </span>
              )}
              {item.status && (
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                  item.status === 'active' ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200' :
                  item.status === 'inactive' ? 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200' :
                  'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200'
                }`}>
                  {item.status}
                </span>
              )}
            </View>
          </View>

          {/* Description */}
          {item.description && (
            <Text className="text-xs text-slate-500 dark:text-slate-400 leading-tight mb-1"
            style={{
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              whiteSpace: 'normal'
            }}>
              {item.description}
            </Text>
          )}

          {/* Subtitle */}
          {item.subtitle && (
            <Text className="text-xs text-slate-400 dark:text-slate-500 leading-tight"
            style={{
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              whiteSpace: 'normal'
            }}>
              {item.subtitle}
            </Text>
          )}
        </View>

        {/* Icon */}
        <View className="flex items-center space-x-2 flex-shrink-0 mt-0.5">
          {getItemIcon(item)}
        </View>
      </View>
    );
  };

  return (
    <View className={`w-full ${className}`} {...props}>
      {/* Label */}
      {label && (
        <Text as="label" className="block text-sm font-semibold text-slate-700 dark:text-slate-200 mb-2">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Text>
      )}

      {/* Main Container */}
      <View className="flex flex-col md:flex-row gap-3 md:gap-4">
        {/* Available Items */}
        <View className="flex-1 bg-white/95 dark:bg-background backdrop-blur-lg border border-slate-200/50 dark:border-border rounded-2xl shadow-sm">
          {/* Header */}
          <View className="p-3 border-b border-slate-200 dark:border-border">
            <View className="flex items-center justify-between mb-2">
              <Text as="h3" className="font-semibold text-slate-700 dark:text-slate-200">
                {sourceTitle}
              </Text>
              {showCount && (
                <View className="flex items-center gap-2">
                  <span className="px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 text-xs rounded-full">
                    {filteredSourceData.length}
                  </span>
                  {maxSelections && (
                    <span className="px-2 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400 text-xs rounded-full border border-primary-200 dark:border-primary-700">
                      {selectedItems.length}/{maxSelections}
                    </span>
                  )}
                </View>
              )}
            </View>

            {/* Search */}
            {searchable && (
              <View className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 w-4 h-4" />
                <SearchBar
                  value={searchTerm}
                  onChange={setSearchTerm}
                  placeholder={placeholder}
                  disabled={disabled}
                  className=" bg-card border-border"
                />
              </View>
            )}

            {/* Select All */}
            {allowSelectAll && filteredSourceData.length > 0 && (
              <View className="flex items-center justify-between mt-3">
                <Button
                  variant="ghost"
                  size="small"
                  onPress={handleSelectAll}
                  disabled={disabled}
                  className="flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
                >
                  <View className={`w-4 h-4 rounded border-2 border-primary-500 mr-2 flex items-center justify-center transition-colors ${
                    filteredSourceData.every(item => selectedIds.has(item.id)) ? 'bg-primary-500' : 'bg-white dark:bg-background'
                  }`}>
                    {filteredSourceData.every(item => selectedIds.has(item.id)) && (
                      <Check className="w-2.5 h-2.5 text-white" />
                    )}
                  </View>
                  Select All
                </Button>
              </View>
            )}
          </View>

          {/* Items List */}
          <View
            className="overflow-y-auto p-2"
            style={{ height: height || "200px" }}
            onKeyDown={(e) => handleKeyDown(e, 'source')}
            tabIndex={0}
          >
            {filteredSourceData.length === 0 ? (
              <View className="flex flex-col items-center justify-center py-8 text-slate-500 dark:text-slate-400">
                <Search className="w-12 h-12 mb-3 opacity-30" />
                <Text as="p" className="text-sm">No items available</Text>
              </View>
            ) : (
              filteredSourceData.map((item, index) =>
                renderListItem(item, index, selectedIds.has(item.id), 'source')
              )
            )}
          </View>
        </View>

        {/* Reset Control */}
        <View className="flex md:flex-col justify-center items-center py-4 md:py-0">
          <Button
            variant="ghost"
            size="small"
            onPress={() => {
              setSelectedIds(new Set());
              onSelectionChange([]);
            }}
            disabled={disabled || selectedItems.length === 0}
            className="flex items-center justify-center w-10 h-10 rounded-lg text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
            title="Clear all selections"
          >
            <RotateCcw className="w-5 h-5" />
          </Button>
        </View>

        {/* Selected Items */}
        <View className="flex-1 bg-white/95 dark:bg-background backdrop-blur-lg border border-slate-200/50 dark:border-border rounded-2xl shadow-sm">
          {/* Header */}
          <View className="p-3 border-b border-slate-200 dark:border-border">
            <View className="flex items-center justify-between mb-2">
              <Text as="h3" className="font-semibold text-slate-700 dark:text-slate-200">
                {selectedTitle}
              </Text>
              {showCount && (
                <span className="px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-300 text-xs rounded-full">
                  {filteredSelectedData.length}
                </span>
              )}
            </View>

            {/* Search */}
            {searchable && (
              <View className="relative">
                {/* <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 dark:text-slate-500 w-4 h-4" /> */}
                {/* <Input
                  ref={selectedSearchInputRef}
                  type="text"
                  value={selectedSearchTerm}
                  onChange={(e) => setSelectedSearchTerm(e.target.value)}
                  placeholder="Search selected items..."
                  className="w-full pl-10 pr-4 py-2.5 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-600 rounded-xl text-sm text-slate-700 dark:text-slate-300 placeholder-slate-400 dark:placeholder-slate-500 focus:outline-none focus:ring-1 focus:ring-primary-500/20 focus:border-primary-400 transition-all duration-200"
                  disabled={disabled}
                /> */}
                <SearchBar
                  value={selectedSearchTerm}
                  onChange={setSelectedSearchTerm}
                  placeholder="Search selected items..."
                  disabled={disabled}
                  showClearButton
                  className=" bg-card border-border"
                />
              </View>
            )}

            {/* Deselect All */}
            {allowSelectAll && filteredSelectedData.length > 0 && (
              <View className="flex items-center justify-between mt-3">
                <Button
                  variant="ghost"
                  size="small"
                  onPress={handleDeselectAll}
                  disabled={disabled}
                  className="flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300"
                >
                  <View className={`w-4 h-4 rounded border-2 border-primary-500 mr-2 flex items-center justify-center transition-colors ${
                    filteredSelectedData.every(item => selectedIds.has(item.id)) ? 'bg-primary-500' : 'bg-white dark:bg-background'
                  }`}>
                    {filteredSelectedData.every(item => selectedIds.has(item.id)) && (
                      <Check className="w-2.5 h-2.5 text-white" />
                    )}
                  </View>
                  Deselect All
                </Button>
              </View>
            )}
          </View>

          {/* Items List */}
          <View
            className="flex flex-col gap-2 overflow-y-auto p-2"
            style={{ height: height || "200px" }}
            onKeyDown={(e) => handleKeyDown(e, 'selected')}
            tabIndex={0}
          >
            {filteredSelectedData.length === 0 ? (
              <View className="flex flex-col items-center justify-center py-8 text-slate-500 dark:text-slate-400">
                <View className="w-12 h-12 mb-3 opacity-30 flex items-center justify-center">
                  <View className="w-8 h-8 border-2 border-dashed border-slate-300 rounded-lg"></View>
                </View>
                <Text as="p" className="text-sm">No items selected</Text>
              </View>
            ) : (
              filteredSelectedData.map((item, index) =>
                renderListItem(item, index, selectedIds.has(item.id), 'selected')
              )
            )}
          </View>
        </View>
      </View>

      {/* Error Message */}
      {error && (
        <View className="flex items-center gap-2 mt-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-xl">
          <X className="w-4 h-4 text-red-500" />
          <Text as="p" className="text-red-500 text-sm font-medium">{error}</Text>
        </View>
      )}

      {/* Helper Text */}
      <View className="mt-3 text-xs text-slate-500 dark:text-slate-400 space-y-1">
        <Text as="p">• Click items to select/deselect</Text>
        <Text as="p">• Use keyboard navigation with arrow keys</Text>
        {maxSelections && <Text as="p">• Maximum {maxSelections} selections allowed</Text>}
      </View>
    </View>
  );
};

export default TransferList;