import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Search, X, Check, Plus, Users } from 'lucide-react';

const TransferList = ({
  sourceData = [],
  selectedItems = [],
  onSelectionChange = () => {},
  placeholder = "Search items...",
  sourceTitle = "Available Items",
  selectedTitle = "Selected Items",
  height = "400px",
  searchable = true,
  disabled = false,
  showCount = true,
  allowSelectAll = true,
  allowCustomValues = false,
  customValuePlaceholder = "Add custom item...",
  className = "",
  ...props
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSearchTerm, setSelectedSearchTerm] = useState('');
  const [selectedIds, setSelectedIds] = useState(new Set(selectedItems.map(item => item.id)));
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const [activeList, setActiveList] = useState('source');
  const [customValue, setCustomValue] = useState('');
  const [showCustomInput, setShowCustomInput] = useState(false);
  
  const sourceListRef = useRef(null);
  const selectedListRef = useRef(null);
  const searchInputRef = useRef(null);
  const selectedSearchInputRef = useRef(null);
  const customInputRef = useRef(null);

  // Filter items based on search term
  const filteredSourceData = sourceData.filter(item => 
    !selectedIds.has(item.id) && 
    (item.name?.toLowerCase().includes(searchTerm.toLowerCase()) || 
     item.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
     item.department?.toLowerCase().includes(searchTerm.toLowerCase()) ||
     item.label?.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const filteredSelectedData = selectedItems.filter(item => 
    item.name?.toLowerCase().includes(selectedSearchTerm.toLowerCase()) || 
    item.email?.toLowerCase().includes(selectedSearchTerm.toLowerCase()) ||
    item.department?.toLowerCase().includes(selectedSearchTerm.toLowerCase()) ||
    item.label?.toLowerCase().includes(selectedSearchTerm.toLowerCase())
  );

  // Update selected items when prop changes
  useEffect(() => {
    setSelectedIds(new Set(selectedItems.map(item => item.id)));
  }, [selectedItems]);

  // Handle item selection
  const handleItemSelect = useCallback((item) => {
    if (disabled) return;
    
    const newSelectedIds = new Set(selectedIds);
    const newSelectedItems = [...selectedItems];
    
    if (selectedIds.has(item.id)) {
      newSelectedIds.delete(item.id);
      const index = newSelectedItems.findIndex(selected => selected.id === item.id);
      if (index > -1) {
        newSelectedItems.splice(index, 1);
      }
    } else {
      newSelectedIds.add(item.id);
      newSelectedItems.push(item);
    }
    
    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedItems);
  }, [selectedIds, selectedItems, onSelectionChange, disabled]);

  // Handle custom value addition
  const handleAddCustomValue = useCallback(() => {
    if (!customValue.trim() || disabled) return;
    
    const newItem = {
      id: Date.now() + Math.random(),
      name: customValue.trim(),
      label: customValue.trim(),
      isCustom: true
    };
    
    const newSelectedItems = [...selectedItems, newItem];
    const newSelectedIds = new Set([...selectedIds, newItem.id]);
    
    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedItems);
    setCustomValue('');
    setShowCustomInput(false);
  }, [customValue, selectedItems, selectedIds, onSelectionChange, disabled]);

  // Handle select all
  const handleSelectAll = useCallback(() => {
    if (disabled) return;
    
    const newSelectedItems = [...selectedItems];
    const newSelectedIds = new Set(selectedIds);
    
    filteredSourceData.forEach(item => {
      if (!selectedIds.has(item.id)) {
        newSelectedIds.add(item.id);
        newSelectedItems.push(item);
      }
    });
    
    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedItems);
  }, [filteredSourceData, selectedIds, selectedItems, onSelectionChange, disabled]);

  // Handle deselect all
  const handleDeselectAll = useCallback(() => {
    if (disabled) return;
    
    const newSelectedItems = selectedItems.filter(item => 
      !filteredSelectedData.some(filtered => filtered.id === item.id)
    );
    const newSelectedIds = new Set(newSelectedItems.map(item => item.id));
    
    setSelectedIds(newSelectedIds);
    onSelectionChange(newSelectedItems);
  }, [selectedItems, filteredSelectedData, onSelectionChange, disabled]);

  // Keyboard navigation
  const handleKeyDown = useCallback((e, listType) => {
    const currentList = listType === 'source' ? filteredSourceData : filteredSelectedData;
    const maxIndex = currentList.length - 1;
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setActiveList(listType);
        setFocusedIndex(prev => Math.min(prev + 1, maxIndex));
        break;
      case 'ArrowUp':
        e.preventDefault();
        setActiveList(listType);
        setFocusedIndex(prev => Math.max(prev - 1, 0));
        break;
      case 'Enter':
      case ' ':
        e.preventDefault();
        if (focusedIndex >= 0 && focusedIndex < currentList.length) {
          handleItemSelect(currentList[focusedIndex]);
        }
        break;
      case 'Escape':
        setFocusedIndex(-1);
        setShowCustomInput(false);
        break;
    }
  }, [filteredSourceData, filteredSelectedData, focusedIndex, handleItemSelect]);

  // Get icon for item type
  const getItemIcon = (item) => {
    const iconProps = { size: 16, className: "text-slate-500" };
    if (item.isCustom) return <Plus {...iconProps} />;
    return <Users {...iconProps} />;
  };

  // Render list item
  const renderListItem = (item, index, isSelected, listType) => {
    const isActive = activeList === listType && focusedIndex === index;
    
    return (
      <div
        key={item.id}
        className={`
          group flex items-center justify-between p-3 rounded-lg cursor-pointer
          transition-all duration-200 ease-in-out
          ${isSelected 
            ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 shadow-sm' 
            : 'hover:bg-slate-50 border-l-4 border-transparent'
          }
          ${isActive ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
          ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        onClick={() => !disabled && handleItemSelect(item)}
        onMouseEnter={() => setFocusedIndex(index)}
        role="option"
        aria-selected={isSelected}
        tabIndex={isActive ? 0 : -1}
      >
        <div className="flex items-center space-x-3 min-w-0 flex-1">
          <div className="flex-shrink-0">
            {getItemIcon(item)}
          </div>
          <div className="min-w-0 flex-1">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-slate-900 truncate">
                {item.name || item.label}
              </span>
              {item.badge && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {item.badge}
                </span>
              )}
              {item.isCustom && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Custom
                </span>
              )}
            </div>
            {item.email && (
              <p className="text-xs text-slate-500 truncate mt-0.5">
                {item.email}
              </p>
            )}
            {item.department && (
              <p className="text-xs text-slate-400 truncate">
                {item.department}
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2 flex-shrink-0">
          {isSelected && (
            <div className="w-5 h-5 rounded-full bg-blue-500 flex items-center justify-center">
              <Check size={12} className="text-white" />
            </div>
          )}
          <div className={`
            w-4 h-4 rounded border-2 transition-all duration-200
            ${isSelected 
              ? 'bg-blue-500 border-blue-500' 
              : 'border-slate-300 group-hover:border-blue-400'
            }
          `}>
            {isSelected && <Check size={12} className="text-white" />}
          </div>
        </div>
      </div>
    );
  };

  // Render list section
  const renderListSection = (title, items, isSelectedList = false) => {
    const currentSearchTerm = isSelectedList ? selectedSearchTerm : searchTerm;
    const setCurrentSearchTerm = isSelectedList ? setSelectedSearchTerm : setSearchTerm;
    const searchRef = isSelectedList ? selectedSearchInputRef : searchInputRef;
    const listType = isSelectedList ? 'selected' : 'source';
    
    return (
      <div className="flex-1 flex flex-col bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
        {/* Header */}
        <div className="p-4 border-b border-slate-200 bg-gradient-to-r from-slate-50 to-slate-100">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-base font-semibold text-slate-900 flex items-center space-x-2">
              <span>{title}</span>
              {showCount && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-slate-200 text-slate-700">
                  {items.length}
                </span>
              )}
            </h3>
            {allowSelectAll && (
              <div className="flex space-x-2">
                {!isSelectedList && filteredSourceData.length > 0 && (
                  <button
                    onClick={handleSelectAll}
                    disabled={disabled}
                    className="text-xs font-medium text-blue-600 hover:text-blue-700 disabled:opacity-50 transition-colors"
                  >
                    Select All
                  </button>
                )}
                {isSelectedList && filteredSelectedData.length > 0 && (
                  <button
                    onClick={handleDeselectAll}
                    disabled={disabled}
                    className="text-xs font-medium text-red-600 hover:text-red-700 disabled:opacity-50 transition-colors"
                  >
                    Deselect All
                  </button>
                )}
              </div>
            )}
          </div>
          
          {/* Search */}
          {searchable && (
            <div className="relative">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
              <input
                ref={searchRef}
                type="text"
                value={currentSearchTerm}
                onChange={(e) => setCurrentSearchTerm(e.target.value)}
                onKeyDown={(e) => handleKeyDown(e, listType)}
                placeholder={placeholder}
                disabled={disabled}
                className="w-full pl-10 pr-10 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              />
              {currentSearchTerm && (
                <button
                  onClick={() => setCurrentSearchTerm('')}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                >
                  <X size={16} />
                </button>
              )}
            </div>
          )}

          {/* Custom Value Input */}
          {allowCustomValues && isSelectedList && (
            <div className="mt-3">
              {!showCustomInput ? (
                <button
                  onClick={() => setShowCustomInput(true)}
                  disabled={disabled}
                  className="flex items-center space-x-2 w-full p-2 text-left text-sm text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-colors disabled:opacity-50"
                >
                  <Plus size={16} />
                  <span>Add custom item</span>
                </button>
              ) : (
                <div className="flex space-x-2">
                  <input
                    ref={customInputRef}
                    type="text"
                    value={customValue}
                    onChange={(e) => setCustomValue(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        handleAddCustomValue();
                      } else if (e.key === 'Escape') {
                        setShowCustomInput(false);
                        setCustomValue('');
                      }
                    }}
                    placeholder={customValuePlaceholder}
                    disabled={disabled}
                    className="flex-1 px-3 py-2 text-sm border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50"
                    autoFocus
                  />
                  <button
                    onClick={handleAddCustomValue}
                    disabled={disabled || !customValue.trim()}
                    className="px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Add
                  </button>
                  <button
                    onClick={() => {
                      setShowCustomInput(false);
                      setCustomValue('');
                    }}
                    className="px-3 py-2 text-slate-600 hover:text-slate-800 transition-colors"
                  >
                    <X size={16} />
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* List */}
        <div 
          className="flex-1 overflow-y-auto p-2 space-y-1"
          style={{ height }}
          ref={isSelectedList ? selectedListRef : sourceListRef}
          role="listbox"
          aria-multiselectable="true"
          tabIndex={0}
          onKeyDown={(e) => handleKeyDown(e, listType)}
        >
          {items.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-slate-500">
              <div className="w-12 h-12 rounded-full bg-slate-100 flex items-center justify-center mb-3">
                <Search size={20} className="text-slate-400" />
              </div>
              <p className="text-sm text-center">
                {currentSearchTerm ? 'No items match your search' : 'No items available'}
              </p>
            </div>
          ) : (
            items.map((item, index) => 
              renderListItem(
                item, 
                index, 
                selectedIds.has(item.id), 
                listType
              )
            )
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={`w-full ${className}`} {...props}>
      <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
        {renderListSection(sourceTitle, filteredSourceData, false)}
        {renderListSection(selectedTitle, filteredSelectedData, true)}
      </div>
    </div>
  );
};

export default TransferList;