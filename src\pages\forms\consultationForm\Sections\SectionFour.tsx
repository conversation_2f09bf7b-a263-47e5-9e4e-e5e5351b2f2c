import React, { useEffect } from "react";
import Input from "@/components/input";
import Select from "@/components/Select";
import View from "@/components/view";
// import { Appointment } from "@/interfaces/appointments";
import useForm from "@/utils/custom-hooks/use-form";
// import Textarea from "@/components/Textarea";
// import SearchSelect from "@/components/SearchSelect";
// import { useOpd } from "@/actions/calls/opd";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
// import { useEffect } from "react";
// import dayjs from "dayjs";
import TipTapTextEditor from "@/components/TipTapTexteditor";
import { Consultation } from "@/interfaces/consultation";
import { useTest } from "@/actions/calls/test";
import SearchSelect from "@/components/SearchSelect";
import MultiSelectWithDropDown from "@/components/MultiSelectWithDropDown";
import MultiSelector from "@/components/MultiSelector";
import SingleSelector from "@/components/SingleSelector";
import TransferList from "@/components/TransferList";
import { Card } from "@/components/ui/card";
import Text from "@/components/text";
import { useMedicine } from "@/actions/calls/medicine";
import MedicinesSection from "@/components/MedicinesSection";
// import Text from "@/components/text";
// import Button from "@/components/button";

interface SectionFourProps {
  // errorsTemperature: string;
  // errorsBp: string;
  // errorsPulse: string;
  // errorsCvs: string;
  // errorsRs: string;
  // errorsTest: string;
  postExaminationData: any;
  mainOnSetHandler: (name: string, value: any) => void;
}

const SectionFour: React.FC<SectionFourProps> = ({
  // errorsTemperature,
  // errorsBp,
  // errorsPulse,
  // errorsCvs,
  // errorsRs,
  // errorsTest,
  postExaminationData,
  mainOnSetHandler,
}) => {
  // const examinationDetails = useSelector(
  //   (state: RootState) => state.examinations.examinationDetails
  // );
  // const { values, handleChange } = useForm<Examination | null>(
  //   examinationDetails
  // );
    const { medicineDropdownHandler } = useMedicine();
  

  const consultationDetail = useSelector(
    (state: RootState) =>
      state.consultation.consultationDetailData?.vitals
  );

  const medicineDropdownData = useSelector(
      (state: RootState) => state.medicines.medicineDropdownData
    )?.map((item: any) => ({
      id: item?.id,
      label: item?.medicine_name,
      value: item?.medicine_name,
    }));

    useEffect(() => {
        medicineDropdownHandler(() => {});
      }, []);



    // const testIds = testData?.split(",")?.map((item: any) => item.trim());
    // const testLabelMap = testObj?.filter((item: any) =>
    //   testIds?.includes(item?.value?.toString())
    // )?.map((item: any) => {
    //   return {
    //     id: item?.value,
    //     label: item?.label,
    //     value: item?.value,
    //   };
    // });
    // const testLabelMap = testObj?.filter((item: any) =>
    //   testIds?.includes(item?.value?.toString())
    // )?.map((item: any) => item?.label)?.join(",");
    // console.log("testLabelMap", testLabelMap);

    

  const { values, handleChange, handleTipTapChange,onSetHandler } =
    useForm<Consultation | null>(consultationDetail);

  return (
    <React.Fragment>
      <View>
        {/* Plan  */}
        <TipTapTextEditor
          name="plan"
          value={values?.plan || ""}
          onChange={handleTipTapChange}
          label="Plan"
          placeholder="Enter plan..."
        />
      </View>
      
      <Card className="mt-2 ">
        <Text className="text-lg font-bold pb-2 mb-2">
          Estimated Cost  
        </Text>
        <View className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-background p-4">
          <View>
             <Input 
            id="amount"
            name="amount"
            label="Amount"
            onChange={handleChange}
            value={values?.amount ? values?.amount + "" : ""}
            placeholder="Enter Amount"
            className="bg-card"
          />
          </View>
        <View>
          <SingleSelector
            id="currency"
            label="Currency"
            name="currency"
            value={values?.currency || ""}
            placeholder="Select Currency"
            onChange={(value) => {
              onSetHandler("currency", value);
            }}
            options={[
              { label: "INR", value: "INR" },
              { label: "USD", value: "USD" },
              { label: "EUR", value: "EUR" },
            ]}
            className="bg-card"
          />
        </View>
        </View>
      </Card>

      {/* Diat plan  */}
      <View>
        <TransferList
              name="diat_plan"
              label="Diat Plan"
              sourceData={[
                { id: 1, label: "Diet 1", value: "diet_1" },
                { id: 2, label: "Diet 2", value: "diet_2" },
                { id: 3, label: "Diet 3", value: "diet_3" },
                { id: 4, label: "Diet 4", value: "diet_4" },
              ]}
              selectedItems={[]}
              onSelectionChange={(value) => {
                console.log("value", value);
              }}
              placeholder="Search diat plan..."
              sourceTitle="Available Diat Plan"
              selectedTitle="Selected Diat Plan"
              height="150px"
              searchable
              showCount
              allowSelectAll
              // allowCustomValues
              // customValuePlaceholder="Add custom diat plan"
            />
      </View>

      {/* Medicines  */}
       <View className=" rounded-lg">
            <MedicinesSection
              // errorsDosage={errorsDosage}
              // errorsTiming={errorsTiming}
              // errorsMedicines={errorsMedicines}
              medicinesList={medicineDropdownData}
              medicineData={postExaminationData?.medicines}
              onSetHandler={onSetHandler}
            />
          </View>

    </React.Fragment>
  );
};
export default SectionFour;
