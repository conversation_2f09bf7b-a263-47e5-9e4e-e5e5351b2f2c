import { useConsultation } from "@/actions/calls/consultation";
import Button from "@/components/button";
import ActionMenu from "@/components/editDeleteAction";
import Modal from "@/components/Modal";
import PaginationComponent from "@/components/Pagination";
import DataSort, { SortOption } from "@/components/SortData";
import Text from "@/components/text";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import SearchBar from "@/components/ui/search-bar";
import View from "@/components/view";
import { handleSortChange } from "@/utils/helperFunctions";
import {
  CONSULTATION_DETAILS_URL,
  CONSULTATION_EDIT_URL,
  CONSULTATION_TABLE_URL,
} from "@/utils/urls/frontend";
import dayjs from "dayjs";
import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { useNavigate, useSearchParams } from "react-router-dom";
import Filter from "../filter";
import Input from "@/components/input";
import { toast } from "@/utils/custom-hooks/use-toast";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
// import { toast } from "@/utils/custom-hooks/use-toast";
import { useInvoice } from "@/actions/calls/invoice";
import Select from "@/components/Select";

const ConsultationPage: React.FC<{}> = () => {
  const navigate = useNavigate();
  const { downloadPrescroriptionHandler } = useInvoice();
  const { consultationListHandler, consultationDeleteHandler, cleanUp } =
    useConsultation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [deleteId, setDeleteId] = useState<null | string>(null);
  const consultationListData = useSelector(
    (state: any) => state.consultation.consultationListData
  );
  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );
  useEffect(() => {
    if (searchParams?.has("currentPage")) {
      consultationListHandler(
        searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null,
        filterData
      );
    }
    return () => {
      cleanUp();
    };
  }, [
    filterData,
    searchParams?.get("currentPage"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
  ]);
  const consultationDownloadHandler = (id: string) => {
    if (id) {
      downloadPrescroriptionHandler(id, async (success: boolean) => {
        if (success) {
          toast({
            title: "Success!",
            description: "Successfully downloaded consultation",
            variant: "success",
          });
        } else {
          toast({
            title: "Error",
            description: "Failed to download consultation",
            variant: "destructive",
          });
        }
      });
    }
  };

  const modalCloseHandler = () => {
    setDeleteId(null);
  };

  const sortOptions: SortOption[] = [
    // { label: "Appointment ID (A-Z)", value: "appointment_id", order: "asc" },
    // { label: "Appointment ID (Z-A)", value: "appointment_id", order: "desc" },
    { label: "Patient ID (A-Z)", value: "patient_id", order: "asc" },
    { label: "Patient ID (Z-A)", value: "patient_id", order: "desc" },
    // { label: "Doctor ID (A-Z)", value: "doctor_id", order: "asc" },
    // { label: "Doctor ID (Z-A)", value: "doctor_id", order: "desc" },
    { label: "Next Visit Date (A-Z)", value: "next_visit_date", order: "asc" },
    { label: "Next Visit Date (Z-A)", value: "next_visit_date", order: "desc" },
    { label: "Status (A-Z)", value: "status", order: "asc" },
    { label: "Status (Z-A)", value: "status", order: "desc" },
    { label: "Payment Status (A-Z)", value: "payment_status", order: "asc" },
    { label: "Payment Status (Z-A)", value: "payment_status", order: "desc" },
    // { label: "Complaint (A-Z)", value: "complaint", order: "asc" },
    // { label: "Complaint (Z-A)", value: "complaint", order: "desc" },
    // { label: "Advice (A-Z)", value: "advice", order: "asc" },
    // { label: "Advice (Z-A)", value: "advice", order: "desc" },
  ];
  const [activeSort, setActiveSort] = useState<SortOption | null>(null);

  return (
    <React.Fragment>
      <Modal
        title="Consultation Delete"
        isOpen={deleteId ? true : false}
        onClose={modalCloseHandler}
        description="Are you sure you want to delete this data? This action cannot be undone and will permanently remove the data from the system."
      >
        <View className="flex justify-end gap-2">
          <Button variant="outline" onPress={modalCloseHandler}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onPress={() => {
              if (deleteId) {
                consultationDeleteHandler(deleteId, (success: boolean) => {
                  if (success) {
                    consultationListHandler(
                      searchParams?.get("currentPage") ?? 1,
                      () => {
                        modalCloseHandler();
                      }
                    );
                  }
                });
              }
            }}
          >
            Delete
          </Button>
        </View>
      </Modal>
      <View className="mb-6">
        <Text
          as="h1"
          weight="font-semibold"
          className="text-2xl font-bold text-text-DEFAULT mb-1"
        >
          Consultation
        </Text>
        <Text as="p" className="text-text-light">
          View and manage all patient consultations
        </Text>
      </View>

      <Card className="overflow-hidden">
        {/* <View className="p-4 border-b border-neutral-200 bg-card flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center  dark:border-none">
          <View className="flex gap-2 w-full  justify-between items-center ">
            <SearchBar
              onSearch={(value: string) => {
                setSearchParams(
                  {
                    ...Object.fromEntries([...searchParams]),
                    currentPage: "1",
                    search: value,
                  },
                  { replace: true }
                );
              }}
              className="shadow-sm dark:shadow-none"
            />
            <View className="flex gap-3">
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
              <Filter
                title="Patient Filter"
                onResetFilter={() => {
                  setFilterData(null);
                }}
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="patient_number" placeholder="Patient Number" />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="appointment_number"
                      placeholder="Appointment Number"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="doctor_name" placeholder="Doctor Name" />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      type="text"
                      name="next_visit_date"
                      placeholder="Next Visit Date"
                      onFocus={(e) => (e.target.type = "date")}
                    />
                  </View>,
                ]}
              />
            </View>
          </View>
        </View> */}
        {/* Table */}
        <DynamicTable
          tableHeaders={[
            "Patient Number",
            // "Appointment Number",
            // "Doctor Name",
            "Next Visit Date",
            "Status",
            "Payment Status",
            "Actions",
          ]}
          tableData={consultationListData?.data?.map((data: any) => [
            <Link
              to={
                CONSULTATION_TABLE_URL +
                CONSULTATION_DETAILS_URL +
                "/" +
                data.id
              }
              className="font-medium text-text-DEFAULT hover:text-secondary hover:underline"
            >
              {data?.patient_number}
            </Link>,
            // data?.appointment_number,
            // data?.doctor_name,
            data?.next_visit_date
              ? dayjs(data?.next_visit_date).format("DD-MM-YYYY")
              : "NA",
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(data?.status)}
            >
              {data?.status || "N/A"}
            </Text>,
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(data?.payment_status)}
            >
              {data?.payment_status || "N/A"}
            </Text>,
            <ActionMenu
              onEdit={() =>
                navigate(
                  CONSULTATION_TABLE_URL + CONSULTATION_EDIT_URL + "/" + data.id
                )
              }
              onDelete={() => setDeleteId(data.id)}
              onDownload={() => consultationDownloadHandler(data.id)}
              editTitle="Edit Consultation"
              deleteTitle="Delete Consultation"
              downloadTitle="Download Prescription"

            />,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(value: string) => {
                  setSearchParams(
                    {
                      ...Object.fromEntries([...searchParams]),
                      currentPage: "1",
                      search: value,
                    },
                    { replace: true }
                  );
                }}
                className="shadow-sm dark:shadow-none"
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
            filter: (
              <Filter
                title="Consultation Filter"
                onResetFilter={() => {
                  setFilterData(null);
                }}
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                  setSearchParams(
                    {
                      ...Object.fromEntries([...searchParams]),
                      currentPage: "1",
                    },
                    { replace: true }
                  );
                  
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="patient_number" placeholder="Patient Number" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="referred_by_name" placeholder="Referred By" />
                  </View>,
                  // <View className="w-full my-4">
                  //   <Input
                  //     name="appointment_number"
                  //     placeholder="Appointment Number"
                  //   />
                  // </View>,
                  // <View className="w-full my-4">
                  //   <Input name="doctor_name" placeholder="Doctor Name" />
                  // </View>,
                  <View className="w-full my-4">
                    <Select
                      label="Status"
                      name="status"
                      // value={paymentStatus}
                      // onChange={(e) => {
                      //   setPaymentStatus(e.target.value);
                      //   // onSetHandler("payment_status", e.target.value)
                      // }}
                      placeholder="Select Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                      // error={errorsPaymentStatus}
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Select
                      label="Payment Status"
                      name="payment_status"
                      // value={paymentStatus}
                      // onChange={(e) => {
                      //   setPaymentStatus(e.target.value);
                      //   // onSetHandler("payment_status", e.target.value)
                      // }}
                      placeholder="Select Payment Status"
                      options={[
                        { label: "Pending", value: "Pending" },
                        { label: "Completed", value: "Completed" },
                      ]}
                      // error={errorsPaymentStatus}
                    />
                  </View>,
                  // <View className="w-full my-4">
                  //   <Input
                  //     type="date"
                  //     name="next_visit_date"
                  //     placeholder="Next Visit Date"
                  //     onFocus={(e) => (e.target.type = "date")}
                  //   />
                  // </View>,
                ]}
              />
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={consultationListData?.current_page}
                last_page={consultationListData?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
        {/* <PaginationComponent
          getPageNumberHandler={(page) => {
            setSearchParams(
              {
                ...Object.fromEntries([...searchParams]),
                currentPage: `${page}`,
              },
              { replace: true }
            );
          }}
          last_page={consultationListData?.last_page}
          current_page={consultationListData?.current_page}
        /> */}
      </Card>
    </React.Fragment>
  );
};

export default ConsultationPage;
