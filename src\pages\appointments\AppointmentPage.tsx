import Button from "@/components/button";
import Modal from "@/components/Modal";
import DataSort, { SortOption } from "@/components/SortData";
import Text from "@/components/text";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import SearchBar from "@/components/ui/search-bar";
import View from "@/components/view";
import {
  APPOINTMENT_DETAILS_URL,
  APPOINTMENT_FORM_URL,
  APPOINTMENT_TABLE_URL,
} from "@/utils/urls/frontend";
import { Plus } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAppointments } from "@/actions/calls/appointments";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import PaginationComponent from "@/components/Pagination";
import ActionMenu from "@/components/editDeleteAction";
import { formatTime } from "@/utils/dateTimeUtils";
import { toast } from "@/utils/custom-hooks/use-toast";
import { Link } from "react-router-dom";
import { handleSortChange } from "@/utils/helperFunctions";
import FollowUpModal from "@/components/FollowUpModal";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import Filter from "../filter";
import Input from "@/components/input";
import Select from "@/components/Select";
import { statusOptions } from "../forms/appointmentsForm/appointmentFormOptions";
import { useOpd } from "@/actions/calls/opd";
import SingleSelector from "@/components/SingleSelector";

// import { useOpd } from "@/actions/calls/opd";

export const AppointmentPage = () => {
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();
  const {
    appointmentListHandler,
    deleteAppointmentHandler,
    cleanUp,
    editAppointmentHandler,
    appointmentFeesHandler,
  } = useAppointments();
  // const {PuaListHandler} = useOpd();

  // const patients = useSelector((state: RootState) => state.opd.patientList);
  const [filterData, setFilterData] = useState<null | Record<string, string>>(
    null
  );

  const doctors = useSelector((state: RootState) => state.opd.userList);
  const doctorsObj = doctors?.map((doctor: any) => ({
    id: doctor.id,
    label: doctor.name,
    value: doctor.id,
  }));
  const [deleteId, setDeleteId] = useState<null | string>(null);
  const [followUpModalData, setFollowUpModalData] = useState<{
    id: string;
    number: string;
  } | null>(null);
  const { PuaListHandler } = useOpd();

  useEffect(() => {
    if (searchParams?.has("currentPage")) {
      appointmentListHandler(
        filterData ? 1 : searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null,
        filterData
      );
    }
    return () => {
      cleanUp();
    };
  }, [
    filterData,
    searchParams?.get("currentPage"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
  ]);

  const modalCloseHandler = () => {
    setDeleteId(null);
  };

  const sortOptions: SortOption[] = [
    { label: "Type (A-Z)", value: "type", order: "asc" },
    { label: "Type (Z-A)", value: "type", order: "desc" },
    {
      label: "Appointment Date (A-Z)",
      value: "appointment_date",
      order: "asc",
    },
    {
      label: "Appointment Date (Z-A)",
      value: "appointment_date",
      order: "desc",
    },
    {
      label: "Appointment Time (A-Z)",
      value: "appointment_time",
      order: "asc",
    },
    {
      label: "Appointment Time (Z-A)",
      value: "appointment_time",
      order: "desc",
    },
    { label: "Status (A-Z)", value: "status", order: "asc" },
    { label: "Status (Z-A)", value: "status", order: "desc" },
  ];

  const [activeSort, setActiveSort] = useState<SortOption | null>(null);
  const paginateObj = useSelector(
    (state: RootState) => state.appointment.userCompleteObj
  );

  // useEffect(() => {
  //   if (!searchParams.has("sort_by") && !searchParams.has("sort_order")) {
  //     const defaultSort = sortOptions[0];
  //     setSearchParams({
  //       ...Object.fromEntries([...searchParams]),
  //       currentPage: searchParams.get("currentPage") || "1",
  //       sort_by: defaultSort.value,
  //       sort_order: defaultSort.order || "asc"
  //     }, { replace: true });
  //   }
  // })

  // const handleSortChange = (option: SortOption) => {

  //     setActiveSort(option);
  //     if(option.order){
  //       setSearchParams(
  //         {
  //           ...Object.fromEntries([...searchParams]),
  //           currentPage: "1",
  //           sort_by:  option.value,
  //           sort_order: option.order,
  //         },
  //         { replace: true }
  //       );
  //     }
  //   };
  return (
    <React.Fragment>
      <Modal
        title="Appointment Delete"
        isOpen={deleteId ? true : false}
        onClose={modalCloseHandler}
        description="Are you sure you want to delete this appointment? This action cannot be undone and will permanently remove the data from the system."
      >
        <View className="flex justify-end gap-2">
          <Button variant="outline" onPress={modalCloseHandler}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onPress={() => {
              if (deleteId) {
                deleteAppointmentHandler(deleteId, (_: boolean) => {
                  // if (success) {
                  appointmentListHandler(
                    searchParams?.get("currentPage") ?? 1,
                    () => {
                      modalCloseHandler();
                    }
                  );
                  // }
                });
              }
            }}
          >
            Delete
          </Button>
        </View>
      </Modal>
      {/* follow up modal */}
      <FollowUpModal
        isOpen={!!followUpModalData}
        onClose={() => setFollowUpModalData(null)}
        appointment_number={followUpModalData?.number ?? ""}
        onSubmit={(amount) => {
          if (followUpModalData) {
            const payload = {
              appointment_number: followUpModalData.number,
              amount,
            };

            appointmentFeesHandler(payload, (success: boolean) => {
              if (success) {
                editAppointmentHandler(
                  followUpModalData.id,
                  { type: "Follow-up" },
                  (success: boolean) => {
                    if (success) {
                      toast({
                        title: "Success!",
                        description: "Appointment updated with follow-up fees.",
                        variant: "success",
                      });
                      appointmentListHandler(
                        searchParams?.get("currentPage") ?? 1,
                        () => null
                      );
                      setFollowUpModalData(null);
                    } else {
                      toast({
                        title: "Error!",
                        description: "Failed to update appointment type.",
                        variant: "destructive",
                      });
                    }
                  }
                );
              } else {
                toast({
                  title: "Error!",
                  description: "Failed to submit fees.",
                  variant: "destructive",
                });
              }
            });
          }
        }}
      />

      <View className="mb-6">
        <Text
          as="h1"
          weight="font-semibold"
          className="text-2xl font-bold text-text-DEFAULT mb-1"
        >
          Appointments
        </Text>
        <Text as="p" className="text-text-light">
          Manage hospital Appointments
        </Text>
      </View>

      <Card className="overflow-hidden">
        {/* <View className="p-4 border-b border-neutral-200 bg-card flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center  dark:border-none">
          <View className="flex gap-2 w-full  justify-between items-center ">
            <SearchBar
              onSearch={(value: string) => {
                setSearchParams({
                  ...Object.fromEntries([...searchParams]),
                  currentPage: "1",
                  search: value,
                });
              }}
              className="shadow-sm dark:shadow-none"
            />
            <View className="flex gap-3">
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
              <Filter
                title="Patient Filter"
                onResetFilter={() => {
                  setFilterData(null);
                }}
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                }}
                inputFields={[
                  <View className="w-full my-4">
                    <Input name="type" placeholder="Type" />
                  </View>,
                  <View className="w-full my-4">
                    <Input name="status" placeholder="Status" />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="appointment_time"
                      placeholder="Appointment Time"
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      name="appointment_number"
                      placeholder="Appointment Number "
                    />
                  </View>,
                  <View className="w-full my-4">
                    <Input
                      type="text"
                      onFocus={(e) => (e.target.type = "date")}
                      name="appointment_date"
                      placeholder="Appointment Date"
                    />
                  </View>,
                ]}
              />
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(APPOINTMENT_TABLE_URL + APPOINTMENT_FORM_URL);
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Appointment
              </Button>
            </View>
          </View>
        </View> */}
        {/* Table */}
        <DynamicTable
          tableHeaders={[
            "Appointment Number",
            "Patient Name",
            "Phone",
            "Type",
            "Appointment Date",
            "Appointment Time",
            "Status",
            "Actions",
          ]}
          tableData={paginateObj?.data.map((appointment: any) => [
            <Link
              to={`${APPOINTMENT_TABLE_URL}${APPOINTMENT_DETAILS_URL}/${appointment.id}`}
              className="font-medium text-text-DEFAULT hover:text-secondary hover:underline"
            >
              {appointment?.appointment_number || "N/A"}
            </Link>,
            appointment?.patient_name || "-",
            appointment?.patient_phone || "-",
            appointment?.type,
            // <Select
            //   options={appointmentTypeOptions}
            //   value={appointment?.type}
            //   onChange={(e) => {
            //     const selectedType = e.target.value;

            //     if (appointment?.type !== e.target.value) {
            //       if (selectedType === "Follow-up") {
            //         setFollowUpModalData({
            //           id: appointment.id,
            //           number: appointment?.appointment_number,
            //         });
            //       } else {
            //         editAppointmentHandler(
            //           appointment.id,
            //           { type: e.target.value },
            //           (success: boolean) => {
            //             if (success) {
            //               toast({
            //                 title: "Success!",
            //                 description:
            //                   "Appointment type updated successfully.",
            //                 variant: "success",
            //               });
            //               appointmentListHandler(
            //                 searchParams?.get("currentPage") ?? 1,
            //                 () => {
            //                   null;
            //                 }
            //               );
            //             } else {
            //               toast({
            //                 title: "Error!",
            //                 description: "Something went wrong.",
            //                 variant: "destructive",
            //               });
            //             }
            //           }
            //         );
            //       }
            //     }
            //   }}
            // />,
            appointment?.appointment_date || "N/A",
            formatTime(appointment?.appointment_time),
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(appointment?.status)}
            >
              {appointment.status || "N/A"}
            </Text>,
            // appointment?.status || "N/A",
            <ActionMenu
              onEdit={() =>
                navigate(
                  APPOINTMENT_TABLE_URL +
                    APPOINTMENT_FORM_URL +
                    "/" +
                    appointment.id
                )
              }
              onDelete={() => {
                setDeleteId(appointment.id);
              }}
            />,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(value: string) => {
                  setSearchParams({
                    ...Object.fromEntries([...searchParams]),
                    currentPage: "1",
                    search: value,
                  });
                }}
                className="shadow-sm dark:shadow-none"
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
            filter: (
              <Filter
                apiCall={() => {
                  PuaListHandler(() => {});
                }}
                title="Patient Filter"
                onResetFilter={() => {
                  setFilterData(null);
                }}
                onFilterApiCall={(data) => {
                  setFilterData({
                    multiple_filter: data,
                  });
                }}
                inputFields={[
                  // <View className="w-full my-4">
                  //   <Input
                  //     type="text"
                  //     onFocus={(e) => (e.target.type = "date")}
                  //     name="appointment_date"
                  //     placeholder="Appointment Date"
                  //   />
                  // </View>,
                  <View className="w-full my-4">
                    <Input name="type" placeholder="Type" />
                  </View>,
                  <View className="w-full my-4">
                    <SingleSelector
                      name="status"
                      placeholder="Status"
                      options={statusOptions}
                    />{" "}
                  </View>,
                  <View className="w-full my-4">
                    <SingleSelector
                      name="referred_to"
                      placeholder="referred To"
                      options={doctorsObj}
                    />{" "}
                  </View>,
                  <View className="w-full my-4">
                    <Input name="referred_by_name" placeholder="Reffered By" />
                  </View>,
                  // <View className="w-full my-4">
                  //   <Input
                  //     name="appointment_number"
                  //     placeholder="Appointment Number "
                  //   />
                  // </View>,
                ]}
              />
            ),
            action: (
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(APPOINTMENT_TABLE_URL + APPOINTMENT_FORM_URL);
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Appointment
              </Button>
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={paginateObj?.current_page}
                last_page={paginateObj?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
        {/* <PaginationComponent
          getPageNumberHandler={(page) => {
            setSearchParams({
              ...Object.fromEntries([...searchParams]),
              currentPage: `${page}`,
            });
          }}
          last_page={paginateObj?.last_page}
          current_page={paginateObj?.current_page}
        /> */}
      </Card>
    </React.Fragment>
  );
};
