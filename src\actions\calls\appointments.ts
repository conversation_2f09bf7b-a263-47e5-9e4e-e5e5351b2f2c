import LaunchApi from "../api";
import { useDispatch } from "react-redux";
import { ApiCallback } from "@/interfaces/api";
import { AuthPayload } from "@/interfaces/slices/auth";
import { GENERIC_ERROR_MESSAGE, TRYBLOCK_ERROR_MESSAGE } from "@/utils/message";
import {
  appointmentListSlice,
  appointmentDetailSlice,
  // deletePatientSuccess,
} from "../slices/appointments";
import {
  APPOINTMENTS_ADD_URL,
  APPOINTMENTS_LIST_URL,
  APPOINTMENTS_EIDT_URL,
  APPOINTMENTS_DELETE_URL,
  APPOINTMENT_DETAILS_URL,
  APPOINTMENT_FEES_URL
} from "@/utils/urls/backend";

const api = new LaunchApi();

export const useAppointments = () => {
  const dispatch = useDispatch();

  const appointmentDetailHandler = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.get(
        APPOINTMENT_DETAILS_URL + "/" + id,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(appointmentDetailSlice(response));
            return callback(true, response.data);
          } else {
            callback(false, { success: false });
          }
        }
      );
    } catch (error) {
      callback(false, { success: false, error: GENERIC_ERROR_MESSAGE });
    }
  };
  const appointmentListHandler = async (
    page: number | string = 1,
    callback: ApiCallback,
    search?: string | null,
    sort_by?: string | null,
    sort_order?: string | null,
    from_date?: string | null,
    to_date?: string | null,
    data?: any
  ): Promise<void> => {
    try {
      
      await api.get(
        `${APPOINTMENTS_LIST_URL}?page=${page}${search ? "&search=" + search : ""}${
          sort_by ? "&sort_by=" + sort_by : ""
        }${sort_order ? "&sort_order=" + sort_order : ""}${from_date ? "&from_date=" + from_date : ""}${to_date ? "&to_date=" + to_date : ""}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          
          if (success && statusCode === 200) {
            dispatch(appointmentListSlice(response?.data));
            return callback(true, response.data);
          } else {
            callback(true, { success: false });
          }
        },
        data
      );
    } catch (error) {
      callback(false, { success: false, error: GENERIC_ERROR_MESSAGE });
    }
  };
  const addAppointmentHandler = async <T>(
    data: T,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.post(
        APPOINTMENTS_ADD_URL,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            // dispatch(addPatientSlice());
            callback(true, response.data);
          } else {
            callback(false);
         
          }
        },
        data
      );
    } catch (error) {
      callback(false, { success: false });
    }
  };
  const editAppointmentHandler = async (
    id: string,
    data: any,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.put(
        APPOINTMENTS_EIDT_URL + "/" + id,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            return callback(true, {
              success: true,
              message: response.data?.message,
            });
          } else if (success && statusCode !== 204) {
            return callback(false, { success: false });
          }
        },
        data
      );
    } catch (error) {
      callback(false, { success: false });
    }
  };
  const deleteAppointmentHandler = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.delete(
        APPOINTMENTS_DELETE_URL,
        id,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            // dispatch(deletePatientSuccess(id));
            return callback(true, {
              success: true,
              message: response.data.message,
            });
          } else {
            return callback(false, {
              success: false,
              error: TRYBLOCK_ERROR_MESSAGE,
            });
          }
        }
      );
    } catch (error) {
      return callback(false, { success: false });
    }
  };

  const appointmentFeesHandler = async <T>(
    data: T,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.post(
        APPOINTMENT_FEES_URL,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            callback(true, response.data);
          } else {
            callback(false);
          }
        },
        data
      );
    } catch (error) {
      callback(false, { success: false });
    }
  };
  
  const cleanUp = () => {
    api.cleanup();
  };

  return {
    cleanUp,
    addAppointmentHandler,
    editAppointmentHandler,
    appointmentListHandler,
    deleteAppointmentHandler,
    appointmentDetailHandler,
    appointmentFeesHandler
  };
};
