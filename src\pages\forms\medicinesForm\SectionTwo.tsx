import React from "react";
import Input from "@/components/input";
import Select from "@/components/Select";
import View from "@/components/view";
import useForm from "@/utils/custom-hooks/use-form";
// import SearchSelect from "@/components/SearchSelect";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { Medicine } from "@/interfaces/medicines";
import { dosageFormOptions, strengthUnitOptions } from "./medicinesFormOptions";

// interface SectionOneProps {
// errorsType: string;
// errorsPatientId: string;
// errorsDoctorId: string;
// errorsComplaint: string;
// errorsAppointmentDate: string;
// errorsAppointmentTime: string;
// errorsEnrollFees: string;
// errorsStatus: string;

// }
const SectionTwo: React.FC = ({}) => {
  const medicineDetails = useSelector(
    (state: RootState) => state.medicines.medicineDetailData
  );
  // const [dosage, setDosage] = useState('tablet');
  // const [strength, setStrength] = useState('mg/ml');

  const { values, handleChange } = useForm<Medicine | null>(medicineDetails);

  // const [temp, setTemp] = React.useState(`\u00B0C`);

  return (
    <React.Fragment>
      <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <View className="space-y-2">
          <Select
            label="Dosage Form"
            name="dosage_form"
            placeholder="Select Dosage Form"
            value={values?.dosage_form || ""}
            onChange={handleChange}
            options={dosageFormOptions}
            className=""
            required={true}
          />
          {/* <Input
          id="dosage"
          name="dosage"
          label="Dosage"
          onChange={handleChange}
          // error={errorsEnrollFees}
          value={values?.dosage ? values?.dosage?.split(" ")[0] : "" } 
          placeholder="Ex: 1 tablet"
          required={true}
        /> */}
        </View>
        <View className="space-y-2">
          <Input
            id="strength"
            name="strength"
            label="Strength"
            onChange={handleChange}
            // error={errorsEnrollFees}
            value={values?.strength || ""}
            placeholder="Ex: 10"
            required={true}
          />
        </View>
        <View>
          <Select
            name="strength_unit"
            label="Strength Unit"
            required={true}
            placeholder="Select Strength Unit"
            value={values?.strength_unit || ""}
            onChange={handleChange}
            options={strengthUnitOptions}
            className=""
          />
        </View>
      </View>
    </React.Fragment>
  );
};
export default SectionTwo;
