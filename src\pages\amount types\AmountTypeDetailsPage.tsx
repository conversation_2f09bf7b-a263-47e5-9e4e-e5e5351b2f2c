// import AppointmentIndex from ".";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { useNavigate, useParams } from "react-router-dom";
import { useAmountType } from "@/actions/calls/amountType";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DollarSign, FileText, Hash } from "lucide-react";
import Button from "@/components/button";
import { ArrowLeft } from "lucide-react";
// import parseDescription from "@/utils/parseDescription";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import View from "@/components/view";
import Text from "@/components/text";

const AmountTypeDetailsPage = () => {
  const params = useParams();
  const navigate = useNavigate();

  const { amountTypeDetailHandler } = useAmountType();


  const amountTypeData = useSelector(
    (state: RootState) => state.amountType.amountTypeDetailData
  );

  useEffect(() => {
    if (params?.id) {
      amountTypeDetailHandler(params.id, () => {});
    }
  }, [params?.id]);

  return (
        <View className="min-h-screen p-4">
      <View className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <View className="flex items-center gap-4 mb-6 flex item-center justify-between">
          <View>
            <Text as="h1" weight="font-semibold" className="text-2xl">Amount Type Details</Text>
            {/* <Text as="p" className="text-muted-foreground">Details of Amount Type</Text> */}
          </View>
          <Button variant="outline" className="flex items-center" onPress={() => navigate(-1)}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Home
            </Button>
        </View>

        {/* Amount Type Information Card */}
        <Card>
          <CardHeader>
            <View className="flex items-center justify-between">
              <View>
                <CardTitle className="text-2xl">{amountTypeData?.amount_for}</CardTitle>
                <View className="flex items-center gap-2 mt-2">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <Text as="span" className="text-sm text-muted-foreground">Type ID: {amountTypeData?.id}</Text>
                </View>
              </View>
              <Badge style={getStatusColorScheme(amountTypeData?.status)}>
                {amountTypeData?.status || "N/A"}
              </Badge>
            </View>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <View>
              <Text as="h3" weight="font-semibold" className=" mb-3 flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-muted-foreground" />
                Basic Information
              </Text>
              <View className="grid md:grid-cols-2 gap-4">
                <View>
                  <Text as="label" weight="font-medium" className="text-sm text-muted-foreground">Amount For</Text>
                  <Text as="p" >{amountTypeData?.amount_for || "N/A"}</Text>
                </View>
                <View>
                  <Text as="label" weight="font-medium" className="text-sm text-muted-foreground">Status</Text>
                  <Text>{amountTypeData?.status || "N/A"}</Text>
                </View>
              </View>
            </View>

            {/* Description */}
            {amountTypeData.description && (
              <View className="border-t pt-4">
                <Text as="h3" weight="font-semibold" className="mb-2 flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  Description
                </Text>
                <View dangerouslySetInnerHTML={{
                        __html: amountTypeData?.description || "N/A",
                      }} className="leading-relaxed" ></View>
              </View>
            )}
          </CardContent>
        </Card>
      </View>
    </View>
  )
};

export default AmountTypeDetailsPage;
