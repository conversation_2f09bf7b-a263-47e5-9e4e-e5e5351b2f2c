export enum Theme {
  LIGHT = "light",
  DARK = "dark",
  SYSTEM = "system",
}

export type ColorFormat = `#${string}` | `rgb(${number},${number},${number})`;

export interface SystemSettings {
  hospital_logo: URL | string | File;
  //   hospital_logo: URL | File;
  hospital_name: string;

  hospital_prefix: string; // Example: "HOS-"
  patient_prefix: string; // Example: "PAT-"
  ipd_prefix: string; // Example: "IPD-"
  opd_prefix: string; // Example: "OPD-"
  appointment_prefix: string; // Example: "APT-"
  payment_prefix: string; // Example: "PAY-"
  test_prefix: string; // Example: "TEST-"
  findings_prefix: string; // Example: "FIN-"
  invoice_prefix: string; // Example: "INV-"

  primary_color: ColorFormat;
  bg_primary_color: ColorFormat;
  text_primary_color: ColorFormat;
  secondary_color: ColorFormat;
  bg_secondary_color: ColorFormat;
  text_secondary_color: ColorFormat;
  tertiary_color: ColorFormat;
  bg_tertiary_color: ColorFormat;
  text_tertiary_color: ColorFormat;
  currency_symbol: string;
  currency: string;
  theme: Theme;
        // hospital_prefix: string; // Example: "HOS-"
        // patient_prefix: string; // Example: "PAT-"
        // ipd_prefix: string; // Example: "IPD-"
        // opd_prefix: string; // Example: "OPD-"
        // appointment_prefix: string; // Example: "APT-"
        // payment_prefix: string; // Example: "PAY-"
        // findings_prefix: string; // Example: "FIN-"
        // test_prefix: string; // Example: "TEST-"

  email_notification: boolean;
}
