import React, { useState, useRef, useEffect, useCallback } from "react";
import { Search, X } from "lucide-react";
import Button from "../button";
import View from "../view";
import Input from "../input";

interface SearchBarProps {
  placeholder?: string;
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
  onSearch?: (value: string) => void;
  onClear?: () => void;
  autoFocus?: boolean;
  disabled?: boolean;
  variant?: "default" | "filled" | "outlined";
  size?: "sm" | "md" | "lg";
  debounceMs?: number;
  showClearButton?: boolean;
  ariaLabel?: string;
  id?: string;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search...",
  className = "",
  value: controlledValue,
  onChange,
  onSearch,
  onClear,
  autoFocus = false,
  disabled = false,
  variant = "default",
  size = "md",
  debounceMs = 800,
  showClearButton = true,
  ariaLabel = "Search",
  id
}) => {
  // State for uncontrolled component
  const [internalValue, setInternalValue] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Determine if component is controlled or uncontrolled
  const isControlled = controlledValue !== undefined;
  const value = isControlled ? controlledValue : internalValue;

  // Clear the debounce timer helper
  const clearDebounceTimer = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }
  }, []);

  // Clear the debounce timer on unmount
  useEffect(() => {
    return () => {
      clearDebounceTimer();
    };
  }, [clearDebounceTimer]);

  // Auto focus the input if autoFocus is true
  useEffect(() => {
    if (autoFocus && inputRef.current && !disabled) {
      inputRef.current.focus();
    }
  }, [autoFocus, disabled]);

  // Debounced search function
  const debouncedSearch = useCallback((searchValue: string) => {
    if (onSearch) {
      clearDebounceTimer();
      
      if (debounceMs > 0) {
        debounceTimerRef.current = setTimeout(() => {
          onSearch(searchValue);
        }, debounceMs);
      } else {
        onSearch(searchValue);
      }
    }
  }, [onSearch, debounceMs, clearDebounceTimer]);

  // Handle input change with proper debouncing
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    
    // Update internal state if uncontrolled
    if (!isControlled) {
      setInternalValue(newValue);
    }

    // Call onChange handler immediately
    if (onChange) {
      onChange(newValue);
    }

    // Debounce the onSearch call
    debouncedSearch(newValue);
  };

  // Handle search submission
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Clear any pending debounced search
    clearDebounceTimer();
    
    // Execute search immediately on form submit
    if (onSearch) {
      onSearch(value);
    }
  };

  // Handle clearing the input
  const handleClear = () => {
    const emptyValue = "";
    
    // Clear debounce timer
    clearDebounceTimer();
    
    // Update state
    if (!isControlled) {
      setInternalValue(emptyValue);
    }

    // Call handlers
    if (onChange) {
      onChange(emptyValue);
    }

    if (onClear) {
      onClear();
    }

    if (onSearch) {
      onSearch(emptyValue);
    }

    // Focus the input after clearing
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Handle focus
  const handleFocus = () => {
    setIsFocused(true);
  };

  // Handle blur
  const handleBlur = () => {
    setIsFocused(false);
  };

  // Handle key down for escape key
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      if (value) {
        handleClear();
      } else {
        inputRef.current?.blur();
      }
    }
  };

  // Size classes
  const sizeClasses = {
    sm: "pl-8 pr-8 py-1.5 text-sm",
    md: "pl-10 pr-10 py-2",
    lg: "pl-12 pr-12 py-3 text-lg"
  };

  // Variant classes
  const variantClasses = {
    default: "bg-background  border-none rounded-lg focus:outline-none",
    filled: "bg-white dark:bg-background border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none",
    outlined: "bg-transparent border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none"
  };

  // Icon size based on search bar size
  const iconSize = {
    sm: "w-3.5 h-3.5",
    md: "w-4 h-4",
    lg: "w-5 h-5"
  };

  // Icon positioning
  const iconPosition = {
    sm: 'pl-2.5',
    md: 'pl-3',
    lg: 'pl-4'
  };

  const hasValue = value.length > 0;

  return (
    <form onSubmit={handleSubmit} className={`relative ${className}`} role="search">
      <View className={`absolute inset-y-0 left-0 flex items-center ${iconPosition[size]} pointer-events-none`}>
        <Search 
          className={`${iconSize[size]} text-muted-foreground z-10`} 
          aria-hidden="true" 
        />
      </View>

      <Input
        ref={inputRef}
        id={id}
        type="text"
        value={value}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        aria-label={ariaLabel}
        aria-describedby={hasValue && showClearButton ? `${id}-clear` : undefined}
        autoComplete="off"
        spellCheck="false"
        className={`${sizeClasses[size]} ${variantClasses[variant]} w-full transition-colors duration-200 dark:text-white dark:placeholder-gray-400 ${
          isFocused ? 'ring-2 ring-primary-300 dark:ring-primary-600' : ''
        } ${disabled ? 'opacity-60 cursor-not-allowed' : ''}`}
      />

      {showClearButton && hasValue && !disabled && (
        <Button
          variant="ghost"
          type="button"
          onPress={handleClear}
          className={`absolute inset-y-0 right-0 flex items-center ${iconPosition[size]} text-gray-400 hover:text-gray-500 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none transition-colors duration-200`}
          aria-label="Clear search"
          id={id ? `${id}-clear` : undefined}
        >
          <X className={iconSize[size]} aria-hidden="true" />
        </Button>
      )}
    </form>
  );
};

export default SearchBar;