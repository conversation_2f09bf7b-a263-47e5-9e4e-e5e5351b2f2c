import Button from "@/components/button";
import Input from "@/components/input";
import Text from "@/components/text";
import View from "@/components/view";
import useForm from "@/utils/custom-hooks/use-form";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { validationForm } from "./validationForm";
import { toast } from "@/utils/custom-hooks/use-toast";
import { FormTypeProps } from "@/interfaces/dashboard";
import { useDispatch, useSelector } from "react-redux";
import { useMedicineCategory } from "@/actions/calls/medicineCategory";
import { clearMedicineCategoryDetailSlice } from "@/actions/slices/medicineCategory";
import { RootState } from "@/actions/store";
import { MedicineCategory } from "@/interfaces/medicines/medicine_category";

const MedicineCategoryForm: React.FC<FormTypeProps> = ({
  formType = "add",
}) => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    addMedicineCategoryHandler,
    editMedicineCategoryHandler,
    detailMedicineCategoryHandler,
    cleanUp,
  } = useMedicineCategory();

  useEffect(() => {
    if (formType === "edit" && id) {
      detailMedicineCategoryHandler(id, () => {});
    }
    return () => {
      cleanUp();
      dispatch(clearMedicineCategoryDetailSlice());
    };
  }, [id, formType]);

  const medicineCategoryData = useSelector(
    (state: RootState) => state.medicineCategory.medicineCategoryDetailData
  );
  const { values, handleChange } = useForm<MedicineCategory | null>(
    medicineCategoryData
  );
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    let medicineCategoryFormObj: Partial<MedicineCategory> = {};
    try {
      for (let [key, value] of formData.entries()) {
        medicineCategoryFormObj[key as keyof MedicineCategory] = value as any;
      }
      await validationForm.validate(medicineCategoryFormObj, {
        abortEarly: false,
      });
      setErrors({});
      setIsSubmitting(true);
      if (formType === "add") {
        addMedicineCategoryHandler(
          medicineCategoryFormObj,
          (success: boolean) => {
            if (success) {
              navigate(-1);
              toast({
                title: "Success!",
                description: "Medicine Category Added successfully.",
                variant: "success",
              });
            } else {
              setIsSubmitting(false);
              toast({
                title: "Error!",
                description: "Failed to add Medicine Category",
                variant: "destructive",
              });
            }
          }
        );
      } else if (id) {
        editMedicineCategoryHandler(
          id,
          medicineCategoryFormObj,
          (success: boolean) => {
            if (success) {
              navigate(-1);
              toast({
                title: "Success!",
                description: "Medicine Category Updated successfully.",
                variant: "success",
              });
            } else {
              setIsSubmitting(false);
              toast({
                title: "Error!",
                description: "Failed to update Medicine Category",
                variant: "destructive",
              });
            }
          }
        );
      }
    } catch (error: any) {
      setIsSubmitting(false);
      if (error.inner) {
        const validationErrors: Record<string, string> = {};
        error.inner.forEach((e: any) => {
          validationErrors[e.path] = e.message;
        });
        setErrors(validationErrors);
      }
    }
  };
  return (
    <View className="min-h-screen dark:bg-background flex flex-col  items-center p-4">
      <View className="border border-border dark:bg-card rounded-lg shadow-card w-full max-w-4xl p-6 md:p-8 mb-8">
        <View className=" flex items-center justify-between">
          <Text
            as="h2"
            weight="font-bold"
            className="text-2xl font-bold text-center text-primary mb-2"
          >
            Add Medicine Category
          </Text>

          <Button onPress={() => navigate(-1)} variant="outline">
            Back to Home
          </Button>
        </View>
        <Text as="p" className="text-text-light text-left mb-6">
          {/* {formType === "add" && "Fill in the details to create a new account"} */}
          Fill in the details to create a Medicine Category
        </Text>
        <form onSubmit={handleSubmit}>
          <View>
            <Input
              required={true}
              id="category_name"
              name="category_name"
              label="Category Name"
              onChange={handleChange}
              placeholder="Category Name"
              error={errors?.category_name}
              value={values?.category_name}
            />
          </View>
          <View className="col-span-2 mt-6">
            <Button
              htmlType="submit"
              loading={isSubmitting}
              className="w-full bg-primary text-white rounded-md py-3 font-medium hover:bg-primary-600 transition focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-2"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </View>
        </form>
      </View>
    </View>
  );
};
export default MedicineCategoryForm;
