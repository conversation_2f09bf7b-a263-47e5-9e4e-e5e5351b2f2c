import { ApiCallback } from "@/interfaces/api";
import { useDispatch } from "react-redux";
import {
  invoiceDetailSlice,
  invoiceListSlice,
  paymentDetailSlice,
} from "../slices/invoice";
import { AuthPayload } from "@/interfaces/slices/auth";
import LaunchA<PERSON> from "../api";
import {
  CONSULTATION_REPORT_URL,
  INVOICE_DETAILS_URL,
  INVOICE_DOWNLOAD_URL,
  INVOICE_LIST_URL,
  INVOICE_PAYMENT_DETAILS_URL,
  INVOICE_PAYMENT_URL,
  PRESCRIPTION_DOWNLOAD_URL,
} from "@/utils/urls/backend";
import { TRYBLOCK_ERROR_MESSAGE } from "@/utils/message";

const api = new LaunchApi();

export const useInvoice = () => {
  const dispatch = useDispatch();

  const getInvoiceListHandler = async (
    page: number | string = 1,
    callback: ApiCallback,
    search?: string | null,
    sort_by?: string | null,
    sort_order?: string | null,
    data?: any
  ): Promise<void> => {
    try {
      await api.get(
        `${INVOICE_LIST_URL}?page=${page}${search ? "&search=" + search : ""}${
          sort_by ? "&sort_by=" + sort_by : ""
        }${sort_order ? "&sort_order=" + sort_order : ""}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(invoiceListSlice(response));
            return callback(true, response.data);
          } else {
            callback(false, { success: false });
          }
        },
        data
      );
    } catch (error) {
      callback(false, { success: false });
    }
  };

  const invoicePayment = async (data: any, callback: ApiCallback) => {
    try {
      await api.post(
        `${INVOICE_PAYMENT_URL}`,
        (_: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
             return callback(true);
          } else {
            callback(false, { success: false });
          }
        },
        data
      );
    } catch (error) {
      callback(false);
    }
  };

  const getInvoiceDetailHandler = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.get(
        `${INVOICE_DETAILS_URL}/${id}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(invoiceDetailSlice(response.data));
            return callback(true, response.data);
          } else {
            callback(false, { success: false });
          }
        }
      );
    } catch (error) {
      callback(false, { success: false });
    }
  };

  const getPaymentDetailHandler = async (
    id: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      await api.get(
        `${INVOICE_PAYMENT_DETAILS_URL}/${id}`,
        (response: AuthPayload, success: boolean, statusCode: number) => {
          if (success && statusCode === 200) {
            dispatch(paymentDetailSlice(response.data));
            return callback(true);
          } else {
            callback(false);
          }
        }
      );
    } catch (error) {
      callback(false);
    }
  };

  const downloadInvoiceHandler = async <T>(
    id: T,
    // path: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      // Get your base URL and auth token
      const baseUrl = import.meta.env.VITE_BASE_URL;
      const token = localStorage.getItem("token"); // Your method to get auth token

      // Use XMLHttpRequest for direct binary handling
      const xhr = new XMLHttpRequest();
      xhr.open("GET", `${baseUrl}${INVOICE_DOWNLOAD_URL}/${id}`, true);

      // Set response type to arraybuffer for binary data
      xhr.responseType = "arraybuffer";

      // Add authorization header if needed
      if (token) {
        xhr.setRequestHeader("Authorization", `Bearer ${token}`);
      }

      xhr.onload = function () {
        if (this.status === 200) {
          // Create blob directly from arraybuffer
          const blob = new Blob([this.response], { type: "application/pdf" });
          const url = window.URL.createObjectURL(blob);

          // Open in new tab
          window.open(url, "_blank");

          // Clean up URL object after delay
          setTimeout(() => window.URL.revokeObjectURL(url), 1000);

          callback(true);
        } else {
          console.error("Failed to download PDF:", this.status);
          callback(false, { success: false, error: TRYBLOCK_ERROR_MESSAGE });
        }
      };

      xhr.onerror = function () {
        console.error("XHR error occurred");
        callback(false, { success: false, error: TRYBLOCK_ERROR_MESSAGE });
      };

      xhr.send();
    } catch (error) {
      console.error("Download error:", error);
      callback(false, { success: false });
    }
  };
  const downloadConsultationHandler = async <T>(
    id: T,
    // path: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      // Get your base URL and auth token
      const baseUrl = import.meta.env.VITE_BASE_URL;
      const token = localStorage.getItem("token"); // Your method to get auth token

      // Use XMLHttpRequest for direct binary handling
      const xhr = new XMLHttpRequest();
      xhr.open("GET", `${baseUrl}${CONSULTATION_REPORT_URL}/${id}`, true);

      // Set response type to arraybuffer for binary data
      xhr.responseType = "arraybuffer";

      // Add authorization header if needed
      if (token) {
        xhr.setRequestHeader("Authorization", `Bearer ${token}`);
      }

      xhr.onload = function () {
        if (this.status === 200) {
          // Create blob directly from arraybuffer
          const blob = new Blob([this.response], { type: "application/pdf" });
          const url = window.URL.createObjectURL(blob);

          // Open in new tab
          window.open(url, "_blank");

          // Clean up URL object after delay
          setTimeout(() => window.URL.revokeObjectURL(url), 1000);

          callback(true);
        } else {
          console.error("Failed to download PDF:", this.status);
          callback(false, { success: false, error: TRYBLOCK_ERROR_MESSAGE });
        }
      };

      xhr.onerror = function () {
        console.error("XHR error occurred");
        callback(false, { success: false, error: TRYBLOCK_ERROR_MESSAGE });
      };

      xhr.send();
    } catch (error) {
      console.error("Download error:", error);
      callback(false, { success: false });
    }
  };
  const downloadPrescroriptionHandler = async <T>(
    id: T,
    // path: string,
    callback: ApiCallback
  ): Promise<void> => {
    try {
      // Get your base URL and auth token
      const baseUrl = import.meta.env.VITE_BASE_URL;
      const token = localStorage.getItem("token"); // Your method to get auth token

      // Use XMLHttpRequest for direct binary handling
      const xhr = new XMLHttpRequest();
      xhr.open("GET", `${baseUrl}${PRESCRIPTION_DOWNLOAD_URL}/${id}`, true);

      // Set response type to arraybuffer for binary data
      xhr.responseType = "arraybuffer";

      // Add authorization header if needed
      if (token) {
        xhr.setRequestHeader("Authorization", `Bearer ${token}`);
      }

      xhr.onload = function () {
        if (this.status === 200) {
          // Create blob directly from arraybuffer
          const blob = new Blob([this.response], { type: "application/pdf" });
          const url = window.URL.createObjectURL(blob);

          // Open in new tab
          window.open(url, "_blank");

          // Clean up URL object after delay
          setTimeout(() => window.URL.revokeObjectURL(url), 1000);

          callback(true);
        } else {
          console.error("Failed to download PDF:", this.status);
          callback(false, { success: false, error: TRYBLOCK_ERROR_MESSAGE });
        }
      };

      xhr.onerror = function () {
        console.error("XHR error occurred");
        callback(false, { success: false, error: TRYBLOCK_ERROR_MESSAGE });
      };

      xhr.send();
    } catch (error) {
      console.error("Download error:", error);
      callback(false, { success: false });
    }
  };

  const cleanUp = () => {
    api.cleanup();
  };

  return {
    invoicePayment,
    getInvoiceListHandler,
    downloadInvoiceHandler,
    getPaymentDetailHandler,
    getInvoiceDetailHandler,
    downloadConsultationHandler,
    downloadPrescroriptionHandler,
    cleanUp,
  };
};
