import axios, { AxiosResponse } from "axios";
import { ConfigProps, Headers } from "@/interfaces/api";
import { REFRESH_TOKEN_URL } from "@/utils/urls/backend";
// const apiURL = import.meta.env.REACT_APP_BASE_URL as string;
const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 65000,
});

/* class of hospital management (Parent) */
class HospitalManagement {
  private isSystemActive: boolean;
  private isOnline: boolean = navigator.onLine;
  constructor() {
    this.isSystemActive = false;
    window.addEventListener("online", () => this.updateOnlineStatus(true));
    window.addEventListener("offline", () => this.updateOnlineStatus(false));
  }

  /** Cleans up event listeners */
  public cleanup(): void {
    window.removeEventListener("online", () => this.updateOnlineStatus(true));
    window.removeEventListener("offline", () => this.updateOnlineStatus(false));
  }

  // Update online status based on browser events
  private updateOnlineStatus(status: boolean): void {
    this.isOnline = status;
  }

  public getOnlineStatus(): boolean {
    return this.isOnline;
  }

  public activateSystem(): void {
    this.isSystemActive = true;
  }

  public isActive(): boolean {
    return this.isSystemActive;
  }
}

/* class for system requirement */

class SystemRequrements extends HospitalManagement {
  constructor() {
    super();
  }
  public checkSystemStatus(): boolean {
    return this.getOnlineStatus();
  }
}

/* class of api (Child) */
class LaunchApi extends HospitalManagement {
  private controllers: { [key: string]: AbortController };
  private interceptorId: number | null = null;
  // private refreshPromise: Promise<string> | null = null;
  constructor() {
    super();
    this.controllers = {};
    this.setupTokenRefreshInterceptor();
  }

  /*
   * setupTokenRefreshInterceptor
   * Setup interceptor to refresh token only when API calls are made and token is near expiration
   *  */
  private setupTokenRefreshInterceptor() {
    //response
    this.interceptorId = axiosInstance.interceptors.request.use(
      async (config) => {
        try {
          const token = localStorage.getItem("token");
          const expiresIn = localStorage.getItem("expires_in"); // in minutes
          const loginDate = localStorage.getItem("date");

          if (token && loginDate && expiresIn) {
            const minutesSinceLogin = Math.floor(
              (new Date().getTime() - new Date(loginDate).getTime()) /
                (1000 * 60)
            );
            const expiresInMinutes = parseInt(expiresIn);
            // Refresh token 30 minutes before expiration
            if (minutesSinceLogin > expiresInMinutes - 30) {
              axiosInstance.interceptors.response.use(async (config) => {
                try {
                  let token = localStorage.getItem("token");
                  let exp = localStorage.getItem("expires_in");
                  let date = localStorage.getItem("date");
                  if (token && date) {
                    const min = Math.floor(
                      (new Date().getTime() - new Date(loginDate).getTime()) /
                        (1000 * 60)
                    );
                    if (exp && min > parseInt(exp) - 30) {
                      const response = await axios({
                        method: "POST",
                        headers: {
                          "Content-Type": "application/json",
                        },
                        url: import.meta.env.VITE_BASE_URL + REFRESH_TOKEN_URL,
                        data: {
                          token,
                        },
                      });

                      if (response.status !== 200) {
                        throw new Error("Error refreshing token");
                      }

                      if(response?.data?.api_key){
                        localStorage.setItem("date", new Date().toString());
                        localStorage.setItem(
                          "token",
                          response?.data?.api_key?.original?.token
                        );
                        localStorage.setItem(
                          "expires_in",
                          response?.data?.api_key?.original?.expires_in
                        );
                      }else{
                        localStorage.clear();
                        location.href = "/";
                      }
                      window.location.reload();
                    }
                  }
                } catch (error) {
                  console.error(error);
                }
                return config;
              });
            }
          }
        } catch (error) {
          console.error("Token refresh error:", error);
          this.handleAuthError();
          return Promise.reject(error);
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
  }
  /**
   * cleanup for duplicate axios instance.
   * super.cleanup() same feature for parent class.
   */
  public cleanup(): void {
    if (this.interceptorId !== null) {
      axiosInstance.interceptors.response.eject(this.interceptorId);
      this.interceptorId = null;
    }
    for (const path in this.controllers) {
      this.controllers[path].abort();
      delete this.controllers[path];
    }
    super.cleanup();
  }

  /**
   * getHeaders Performs setting header for api.
   * @param path - The API endpoint path
   */
  private getHeaders(
    path: string,
    type: "application/json" | "multipart/form-data" = "application/json"
  ): Headers {
    const contentType: Headers["Content-Type"] = type;
    // const contentType: Headers["Content-Type"] = "multipart/form-data";
    let headers: Headers = {
      // "Access-Control-Allow-Origin": "*",
      "Content-Type": contentType,
    };
    if (path !== "/login") {
      const token = localStorage.getItem("token");
      let date: any = new Date().toString();
      headers = {
        ...headers,
        Authorization: "Bearer " + token,
        TimeZone: date.match(/\(([A-Za-z\s].*)\)/)[1],
        IpAddress: "",
      };
    }

    return headers;
  }

  private handleAuthError() {
    localStorage.clear();
    window.location.reload();
    window.location.href = "/";
  }

  private catchBlock(
    error: any,
    path: string,
    callback: (data: any, success: boolean, statusCode: number) => void
  ) {
    const { status, data } = error?.response || {};
    // debugger;
    switch (status) {
      case 401:
        // callback(data, false, status);
        if (localStorage.getItem("token")) {
          this.handleAuthError();
        }
        break;
      case 403:
        if (path !== "login") {
          this.handleAuthError();
        }
        break;
      case 500:
        callback(data, false, status);
        break;
      default:
        callback(data, false, status);
        break;
    }
  }
  /**
   * getController Performs to cancel api request.
   * @param path - The API endpoint path
   */
  private getController(path: string): AbortController {
    if (this.controllers[path]) {
      this.controllers[path].abort();
    }
    const controller = new AbortController();
    this.controllers[path] = controller;
    return controller;
  }

  private clearController(path: string): void {
    delete this.controllers[path];
  }

  multiformData<T>(
    path: string,
    data: {
      id: string;
      image: File;
      modal_type: string;
      file_name: string;
      folder_name: string;
    },
    callback: (data: T, success: boolean, statusCode: number) => void
  ) {
    if (!this.getOnlineStatus()) {
      console.warn("System is offline, POST request aborted");
      callback(null as any, false, 0);
      return;
    }

    const controller = this.getController(path);

    const config: ConfigProps = {
      url: path,
      method: "post",
      headers: this.getHeaders(path, "multipart/form-data"),
      data: data,
      signal: controller.signal,
      withCredentials: true,
    };

    axiosInstance
      .request(config)
      .then((res: AxiosResponse<T>) => {
        callback(res.data as T, true, res.status);
        this.clearController(path);
      })
      .catch((error) => {
        this.catchBlock(error, path, (data, success, statusCode) => {
          callback(data, success, statusCode);
          this.clearController(path);
        });
      });
  }

  /**
   * Performs a GET request to the specified path.
   * @param path - The API endpoint path
   * @param callback - Callback function to handle the response
   * @param params - Optional query parameters
   */
  get<T>(
    path: string,
    callback: (data: T, success: boolean, statusCode: number) => void,
    params?: string
  ) {
    if (!this.getOnlineStatus()) {
      console.warn("System is offline, GET request aborted");
      callback(null as any, false, 0);
      return;
    }
    const controller = this.getController(path);
    let config: ConfigProps = {
      url: path,
      method: "get",
      headers: this.getHeaders(path, "application/json"),
      signal: controller.signal,
      withCredentials: true,
    };
    if (params !== undefined) {
      config["params"] = params;
    }
    axiosInstance
      .request(config)
      .then((res: AxiosResponse<T>) => {
        callback(res.data as T, true, res.status);
        this.clearController(path);
      })
      .catch((error) => {
        this.catchBlock(error, path, (data, success, statusCode) => {
          callback(data, success, statusCode);
          this.clearController(path);
        });
      });
  }
  /**
   * Performs a POST request to the specified path.
   * @param path - The API endpoint path
   * @param callback - Callback function to handle the response
   * @param data - Payload
   */
  post<TResponse, TRequest = unknown>(
    path: string,
    callback: (data: TResponse, success: boolean, statusCode: number) => void,
    data?: TRequest
  ) {
    if (!this.getOnlineStatus()) {
      console.warn("System is offline, POST request aborted");
      callback(null as any, false, 0);
      return;
    }
    const controller = this.getController(path);
    let config: ConfigProps = {
      url: path,
      data: data,
      method: "post",
      headers: this.getHeaders(path),
      signal: controller.signal,
      withCredentials: true,
    };
    axiosInstance
      .request(config)
      .then((res: AxiosResponse<TResponse>) => {
        callback(res.data as TResponse, true, res.status);
        this.clearController(path);
      })
      .catch((error) => {
        this.catchBlock(error, path, (data, success, statusCode) => {
          callback(data, success, statusCode);
          this.clearController(path);
        });
      });
  }
  /**
   * Performs a PUT request to the specified path.
   * @param path - The API endpoint path
   * @param callback - Callback function to handle the response
   * @param data - Payload
   */
  put<T>(
    path: string,
    callback: (data: T, success: boolean, statusCode: number) => void,
    data: T
  ) {
    if (!this.getOnlineStatus()) {
      console.warn("System is offline, PUT request aborted");
      callback(null as any, false, 0);
      return;
    }
    const controller = this.getController(path);
    let config: ConfigProps = {
      url: path,
      data: data,
      method: "put",
      headers: this.getHeaders(path),
      signal: controller.signal,
      withCredentials: true,
    };
    axiosInstance
      .request(config)
      .then((res: AxiosResponse<T>) => {
        callback(res.data as T, true, res.status);
        this.clearController(path);
      })
      .catch((error) => {
        this.catchBlock(error, path, (data, success, statusCode) => {
          callback(data, success, statusCode);
          this.clearController(path);
        });
      });
  }
  /**
   * Performs a PATCH request to the specified path.
   * @param path - The API endpoint path
   * @param callback - Callback function to handle the response
   * @param data - Payload
   */
  patch<T>(
    path: string,
    callback: (data: T, success: boolean, statusCode: number) => void,
    data: T
  ) {
    if (!this.getOnlineStatus()) {
      console.warn("System is offline, PATCH request aborted");
      callback(null as any, false, 0);
      return;
    }
    const controller = this.getController(path);
    let config: ConfigProps = {
      url: path,
      data: data,
      method: "put",
      headers: this.getHeaders(path),
      signal: controller.signal,
      withCredentials: true,
    };
    axiosInstance
      .request(config)
      .then((res: AxiosResponse<T>) => {
        callback(res.data as T, true, res.status);
        this.clearController(path);
      })
      .catch((error) => {
        this.catchBlock(error, path, (data, success, statusCode) => {
          callback(data, success, statusCode);
          this.clearController(path);
        });
      });
  }
  /**
   * Performs a DELETE request to the specified path.
   * @param path - The API endpoint path
   * @param id - Id of the row
   * @param callback - Callback function to handle the response
   */
  delete<T>(
    path: string,
    id: string,
    callback: (data: T, success: boolean, statusCode: number) => void
  ) {
    if (!this.getOnlineStatus()) {
      console.warn("System is offline, DELETE request aborted");
      callback(null as any, false, 0);
      return;
    }
    const controller = this.getController(path);
    let config: ConfigProps = {
      method: "delete",
      url: id ? `${path}/${id}` : path,
      headers: this.getHeaders(path),
      signal: controller.signal,
      withCredentials: true,
    };
    axiosInstance
      .request(config)
      .then((res: AxiosResponse<T>) => {
        callback(res.data as T, true, res.status);
        this.clearController(path);
      })
      .catch((error) => {
        this.catchBlock(error, path, (data, success, statusCode) => {
          callback(data, success, statusCode);
          this.clearController(path);
        });
      });
  }
}

export default LaunchApi;
export { SystemRequrements };
