import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  <PERSON>,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Button from "@/components/button";
import View from "@/components/view";
import Text from "@/components/text";
import { useDispatch, useSelector } from "react-redux";
import { useAllergies } from "@/actions/calls/allergies";
import { clearAllergies } from "@/actions/slices/allergies";

const AllergyDetail = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const { allergyDetailHandler, cleanUp } = useAllergies();
  const allergyData = useSelector(
    (state: any) => state.allergies.allergiesDetailData
  );

  useEffect(() => {
    if (id) {
      allergyDetailHandler(id, () => {});
      setLoading(false);
    }
    return () => {
      cleanUp();
      dispatch(clearAllergies());
    };
  }, [id]);

  if (loading) {
    return (
      <View className="text-center text-muted py-10">
        Loading allergy data...
      </View>
    );
  }
  return (
    <View className="container mx-auto p-4 space-y-6">
      <View className="flex justify-between items-center">
        <View>
          <Text as="h1" className="text-2xl font-bold">
            Allergy Details
          </Text>
          <Text as="p" className="text-muted-foreground">
            Viewing details for Allergy: {allergyData?.test_name}
          </Text>
        </View>
        <View className="flex gap-3">
          <Button variant="outline">
            <Link to="/">Back to Home</Link>
          </Button>
        </View>
      </View>

      <Card>
        <CardHeader>
          <View className="flex items-center justify-between">
            <View>
              <CardTitle className="text-2xl">
                {allergyData.allergen_name}
              </CardTitle>
              <CardDescription>Allergy ID: {allergyData.id}</CardDescription>
            </View>
            <Text
              as="span"
              className="px-3 py-1 rounded-full bg-red-100 text-red-800 font-medium text-sm"
            >
              {allergyData.severity}
            </Text>
          </View>
        </CardHeader>
        <CardContent>
          <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <View className="space-y-4">
              <View>
                <Text
                  as="h3"
                  className="text-sm font-medium text-muted-foreground mb-1"
                >
                  Allergen Type
                </Text>
                <Text as="p" className="font-medium">
                  {allergyData.allergen_type}
                </Text>
              </View>

              <View>
                <Text
                  as="h3"
                  className="text-sm font-medium text-muted-foreground mb-1"
                >
                  Reaction Type
                </Text>
                <Text as="p" className="font-medium">
                  {allergyData.reaction_type}
                </Text>
              </View>

              <View>
                <Text
                  as="h3"
                  className="text-sm font-medium text-muted-foreground mb-1"
                >
                  Severity
                </Text>
                <Text as="p" className="font-medium">
                  {allergyData.severity}
                </Text>
              </View>
            </View>

            <View className="space-y-4">
              <View>
                <Text
                  as="h3"
                  className="text-sm font-medium text-muted-foreground mb-1"
                >
                  Date First Experienced
                </Text>
                <Text as="p" className="font-medium">
                  {allergyData.date_first_experienced}
                </Text>
              </View>

              <View>
                <Text
                  as="h3"
                  className="text-sm font-medium text-muted-foreground mb-1"
                >
                  Management
                </Text>
                <Text as="p" className="font-medium">
                  {allergyData.management}
                </Text>
              </View>

              <View>
                <Text
                  as="h3"
                  className="text-sm font-medium text-muted-foreground mb-1"
                >
                  Documented By
                </Text>
                <Text as="p" className="font-medium">
                  {allergyData.documented_by}
                </Text>
              </View>
            </View>
          </View>

          <Separator className="my-6" />

          <View>
            <Text
              as="h3"
              className="text-sm font-medium text-muted-foreground mb-2"
            >
              Notes
            </Text>
            <View className="p-4 bg-muted/30 rounded-md whitespace-pre-wrap">
              {allergyData.notes}
            </View>
          </View>
        </CardContent>
        {/* <CardFooter className="flex justify-end gap-2">
          <Button variant="outline">Edit</Button>
          <Button variant="destructive">Delete</Button>
        </CardFooter> */}
      </Card>
    </View>
  );
};

export default AllergyDetail;
