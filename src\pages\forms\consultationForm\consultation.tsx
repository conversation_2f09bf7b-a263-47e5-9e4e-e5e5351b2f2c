import Button from "@/components/button";
import Text from "@/components/text";
import View from "@/components/view";
import { useNavigate, useParams } from "react-router-dom";
import SectionOne from "./SectionOne";
import SectionTwo from "./SectionTwo";
import { Consultation } from "@/interfaces/consultation";
import validationForm from "./validationForm";
import { FormTypeProps } from "@/interfaces/dashboard";
import { useConsultation } from "@/actions/calls/consultation";
import { useDispatch, useSelector } from "react-redux";
import { clearConsultationDetailSlice } from "@/actions/slices/consultation";
import { toast } from "@/utils/custom-hooks/use-toast";
import Modal from "@/components/Modal";
import MedicinesForm from "../medicinesForm/medicines";
import { Card, CardContent } from "@/components/ui/card";
import ProctologyExaminationSection from "./ProptologySections/ProctologyExaminationSection";
import PostExaminationSection from "./ProptologySections/postExaminationSection";
import NonProctologyExaminationSection from "./Non-proptologySections/Non-proctologyExaminationSection";
import SectionFour from "./SectionFour";
import useForm from "@/utils/custom-hooks/use-form";
import Input from "@/components/input";
import { imageUpload } from "@/actions/calls/uesImage";
import { RootState } from "@/actions/store";
import { useEffect, useState } from "react";

const ConsultationForm: React.FC<FormTypeProps> = ({ formType = "add" }) => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    consultationEditHandler,
    // addConsultationHandler,
    consultationDetailHandler,
    cleanUp,
  } = useConsultation();

  useEffect(() => {
    if (formType === "edit" && id) {
      consultationDetailHandler(id, () => {});
    }
    return () => {
      cleanUp();
      dispatch(clearConsultationDetailSlice());
    };
  }, [id, formType]);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [, setDocUpload] = useState<File[] | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [medicinePopup, setMedicinePopup] = useState<boolean>(false);

  // Add useForm hook to capture medicines and other data set via onSetHandler
  const { values: formValues,onSetHandler } = useForm<Consultation | null>(null);
  const medicineDropdownData = useSelector(
    (state: RootState) => state?.medicines?.medicineDropdownData
  );

  const consultationData = useSelector(
    (state: any) => state?.consultation?.consultationDetailData
  );

  const proctologyData = useSelector(
    (state: any) =>
      state?.consultation?.consultationDetailData?.proctologyOrNonProctology
  );

  const [formData, setFormData] = useState({
    examination_type: consultationData?.consultations?.type || "Proctology",
  });

  useEffect(() => {
    setFormData((prev) => {
      return {
        ...prev,
        examination_type: consultationData?.consultations?.type || "Proctology",
      };
    });
  }, [consultationData?.consultations?.type]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    let consultationFormObj: Partial<Consultation> = {};
    try {
      let medicines: string = "";
      // First, get data from FormData (form inputs)
      for (let [key, value] of formData.entries()) {
        if (key === "temperature") {
          const dateValue = value as string;
          if (dateValue) {
            value =
              dateValue.split(" ")[0] + " " + formData?.get("temperature_unit");
          }
        }
        if (key === "bp") {
          const dateValue = value as string;
          if (dateValue) {
            value = dateValue;
          }
        }
        if (key === "pulse") {
          const dateValue = value as string;
          if (dateValue) {
            value = dateValue;
          }
        }
        if (key === "doc_upload" && value instanceof File) {
          // const file = value as File;
          // if (file.size === 0 || file.name === "") {
          //   continue;
          // }
          // validDocFiles.push(file);
          continue;
        }
        if (key === "medicines") {
          if (medicines) medicines += ",";
          const price = medicineDropdownData.find(
            (item: any) => item.medicine_name === value
          )?.unit_price;
          medicines += (value + "#" + price) as string;
        }
        if (key === "dosage") {
          medicines += ("#" + value) as string;
        }
        if (key === "timing") {
          medicines += ("#" + value) as string;
        }
        if (formData.get("advice_admition") === "on") {
          (consultationFormObj as any)["advice_admition"] = true;
        } else {
          (consultationFormObj as any)["advice_admition"] = false;
        }
        
        // if (key !== "doc_upload") {
        consultationFormObj[key as keyof Consultation] = value as any;
      }
      // Then, merge with useForm values (medicines, doc_upload, etc.)
      
      if (formValues) {
        consultationFormObj = {
          ...consultationFormObj,
          ...formValues,
        };
      }
      // console.log(consultationFormObj)
      // return;
      consultationFormObj["medicines"] = medicines;
      // Handle doc_upload with separated URLs and Files
      const formValuesAny = formValues as any;
      if (formValuesAny?.existing_file_urls || formValuesAny?.new_files) {
        const existingUrls = formValuesAny?.existing_file_urls ?
          formValuesAny?.existing_file_urls?.split(',')?.filter((url: string) => url?.trim()) : [];
        const newFiles = formValuesAny?.new_files || [];

        // Include both existing URLs and new Files in doc_upload
        // const combinedFiles = [...existingUrls, ...newFiles];
        const combinedFiles = [...existingUrls, ...newFiles].filter(
  (item) => typeof item === "string" || (item instanceof File && item.name)
);
        console.log("combinedFiles", combinedFiles);
        (consultationFormObj as any)["doc_upload"] = combinedFiles;
        

        // Set docUpload state for file upload
        // const actualFiles = newFiles.filter((file: any) => file instanceof File);
        // setDocUpload(actualFiles);
        setDocUpload(combinedFiles);

      } else if (formValues?.doc_upload) {
        // Fallback to original method
        consultationFormObj["doc_upload"] = formValues.doc_upload;
        setDocUpload(Array.isArray(formValues.doc_upload) ?
          formValues.doc_upload.filter((item: any) => item instanceof File) : []);
      }

      delete consultationFormObj["temperature_unit"];
      delete consultationFormObj["doc_upload"];
      delete consultationFormObj["dosage"];
      delete consultationFormObj["timing"];
      
      await validationForm.validate(consultationFormObj, { abortEarly: false });
      setErrors({});

      setIsSubmitting(true);

      // console.log("consultationFormObj", consultationFormObj);
      // return;

      // if (id) {
      //   consultationEditHandler(
      //     id,
      //     consultationFormObj,
      //     (success: boolean, response: any) => {
      //       setIsSubmitting(false);
      //       if (success && response?.data?.id) {
      //         if (docUpload && typeof docUpload !== "string") {
    

      // if (formType === "add") {
      //   addConsultationHandler(consultationFormObj, (success: boolean, response: any) => {
      //     if (success && response?.data?.id) {
      //       if(docUpload && typeof docUpload !== "string") {
      //           // console.log("docUpload", docUpload);
                
      //           console.log(response?.data?.id, "Response Id");
      //           console.log(docUpload, "Files");
                
                
      //       }
      //       // navigate(-1);
      //       toast({
      //         title: "Success!",
      //         description: "Consultation Added successfully.",
      //         variant: "success",
      //       });
      //     } else {
      //       setIsSubmitting(false);
      //       toast({
      //         title: "Error!",
      //         description: "Failed to add Consultation",
      //         variant: "destructive",
      //       });
      //     }
      //   });
      // } else 
      if (id && typeof id === "string") {
        consultationEditHandler(id, consultationFormObj, (success: boolean, response: any) => {
          if (success && response?.data?.id) {
       
            if(formValues?.doc_upload) {
              
                // docUpload.forEach((file: File | string |null) => {
                //   console.log("file", file);
                  
                //     const imageUploaddata = {
                //               id: response?.data?.id,
                //               modal_type: "proctology",
                //               file_name: "doc_upload",
                //               folder_name: "consultation_image",
                //               image: file,
                //             };
                //             imageUpload(imageUploaddata, (uploadSuccess, _) => {
                //               if (!uploadSuccess) {
                //                 toast({
                //                   title: "Error!",
                //                   description: "Failed to upload documents",
                //                   variant: "destructive",
                //                 });
                //               }
                //             });
                // });

                const imageUploaddata = {
                  id: response?.data?.id,
                  modal_type: "proctology",
                  file_name: "doc_upload",
                  folder_name: "consultation_image",
                  image: formValues?.doc_upload?.map((data)=>{
                    if(data instanceof File){
                      return data;
                    }
                  }),
                  oldImage: formValues?.doc_upload? formValues?.doc_upload?.filter((data)=>{
                    return typeof data === "string";
                  }) : []
                };
                imageUpload(imageUploaddata, (uploadSuccess, _) => {
                  if (!uploadSuccess) {
                    toast({
                      title: "Error!",
                      description: "Failed to upload documents",
                      variant: "destructive",
                    });
                  }
                });
              }
              navigate(-1);
              toast({
                title: "Success!",
                description: "Consultation Updated successfully.",
                variant: "success",
              });
            } else {
              setIsSubmitting(false);
              toast({
                title: "Error!",
                description: "Failed to update Consultation",
                variant: "destructive",
              });
            }
          }
        );
      }
    } catch (error: any) {
      console.error("Validation Error:", error, errors);
      setIsSubmitting(false);
      if (error.inner) {
        const validationErrors: Record<string, string> = {};
        error.inner.forEach((e: any) => {
          validationErrors[e.path] = e.message;
        });
        setErrors(validationErrors);
      }
    }
  };

  const button = {
    Proctology: (
      <Button
        type="button"
        variant={
          formData.examination_type === "Proctology" ? "primary" : "outline"
        }
        style={{
          width: "100%",
        }}
        onClick={() => {
          setFormData({
            ...formData,
            examination_type: "Proctology",
          });
          // onSetHandler("type", "Proctology");
        }}
      >
        Proctology
      </Button>
    ),
    "Non Proctology": (
      <Button
        style={{
          width: "100%",
        }}
        variant={
          formData.examination_type === "Non Proctology" ? "primary" : "outline"
        }
        onClick={() => {
          setFormData({
            ...formData,
            examination_type: "Non Proctology",
          });
          // onSetHandler("type", "Non Proctology");
        }}
      >
        Non Proctology
      </Button>
    ),
  };

  return (
    <View className="min-h-screen dark:bg-background flex flex-col  items-center p-4">
      <View className="bg-white border border-border dark:bg-card rounded-lg shadow-card dark:border-none w-full max-w-4xl p-6 md:p-8 mb-8">
        <View className=" flex items-center justify-between">
          <Text
            as="h2"
            weight="font-bold"
            className="text-2xl font-bold text-center text-primary mb-2"
          >
            Consultation Form
          </Text>

          <Button onPress={() => navigate(-1)} variant="outline">
            Back to Home
          </Button>
        </View>
        <Text as="p" className="text-text-light text-left mb-6">
          {/* {formType === "add" && "Fill in the details to create a new account"} */}
          Fill in the details to create a Consultation
        </Text>
        {medicinePopup ? (
          <Modal
            size="full"
            showCloseButton
            title="Add Medicines"
            isOpen={medicinePopup}
            onClose={() => setMedicinePopup(false)}
          >
            <MedicinesForm />
          </Modal>
        ) : (
          <form onSubmit={handleSubmit}>
            <View className="space-y-4">
              <Text
                as="h3"
                className="text-lg border-b pb-2 mb-4 text-text-DEFAULT"
                weight="font-bold"
              >
                Consultation Details
              </Text>
              <SectionOne
                errorsDoctorId={errors.doctor_id}
                errorsPatientId={errors.patient_id}
                errorsComplaint={errors.complaint ?? ""}
                errorsAppointmentId={errors.appointment_id}
              />
            </View>
            <View className="space-y-4 mt-8">
              <Text
                as="h3"
                className="text-lg border-b pb-2 mb-4 text-text-DEFAULT"
                weight="font-bold"
              >
                Primary Medical Vitals Examination
              </Text>
              <SectionTwo
                errorsTemperature={errors.temperature}
                errorsBp={errors.bp}
                errorsPulse={errors.pulse}
                errorsCvs={errors.cvs}
                errorsRs={errors.rs}
                errorsTest={errors.test_id}
                mainOnSetHandler={onSetHandler}
              />
            </View>

            <View className="space-y-4 mt-8">
              <Text
                as="h3"
                className="text-lg border-b pb-2 mb-4 text-text-DEFAULT"
                weight="font-bold"
              >
                Examination Type
              </Text>
              <Card className="shadow-none border-none" style={{boxShadow: "none"}}>
                <CardContent
                  className="flex gap-4 !items-center"
                  style={{ paddingBottom: "0", paddingLeft: "0" }}
                >
                  {consultationData?.consultations?.type === "None" ? (
                    <>
                      {button["Proctology"]}
                      {button["Non Proctology"]}
                    </>
                  ) : (
                    button[
                      consultationData?.consultations
                        ?.type as keyof typeof button
                    ]
                  )}
                </CardContent>
              </Card>
              <Input name="type" hidden value={formData.examination_type} />
            </View>
            <View>
              {formData.examination_type === "Proctology" && (
                <>
                  <ProctologyExaminationSection
                    errorsPreliminaryDiagnostic={errors.preliminary_diagnostic}
                    errorsFindings={errors.findings}
                    errorsExaminationOverview={errors.examination_overview}
                    errorsDocUpload={errors.doc_upload}
                    errorsDiagnosisSummary={errors.diagnosis_summary}
                    errorsAdviceField={errors.advice_field}
                    mainOnSetHandler={onSetHandler}
                  />
                  <PostExaminationSection
                    appointmetnFees={consultationData?.consultations?.fees}
                    postExaminationData={proctologyData}
                    errorsFees={errors.fees}
                    errorsAdviceAdmission={errors.advice_admition}
                    errorsMedicines={errors.medicines}
                    errorsDosage={errors.dosage}
                    errorsTiming={errors.timing}
                  />
                </>
              )}
              {formData.examination_type === "Non Proctology" && (
                <NonProctologyExaminationSection
                  errorsPreliminaryDiagnostic={errors.preliminary_diagnostic}
                  errorsFindings={errors.findings}
                  errorsExaminationOverview={errors.examination_overview}
                  errorsDocUpload={errors.doc_upload}
                  errorsDiagnosisSummary={errors.diagnosis_summary}
                  errorsAdviceField={errors.advice_field}
                  errorsMedicines={errors.medicines}
                  errorsDosage={errors.dosage}
                  errorsTiming={errors.timing}
                  nonProctologyData={proctologyData}
                />
              )}
              {/* <PostExaminationSection /> */}
              <SectionFour
                paymentStatus={consultationData?.consultations?.payment_status}
                status={consultationData?.consultations?.status}
                errorsStatus={errors.status}
                errorsPaymentStatus={errors.payment_status}
                errorNextVisitDate={errors.next_visit_date}
              />
            </View>

            {/* <SectionThree onSetMedicinePopup={setMedicinePopup} 
            errorsDescription={errors.description}
            errorsExaminationOverview={errors.examination_overview}
            errorsMedicines={errors.medicines}
            /> */}
            {/* </View> */}
            <View className="col-span-2 mt-6">
              <Button
                htmlType="submit"
                loading={isSubmitting}
                className="w-full bg-primary text-white rounded-md py-3 font-medium hover:bg-primary-600 transition focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-2"
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </View>
          </form>
        )}
      </View>
    </View>
  );
};

export default ConsultationForm;
