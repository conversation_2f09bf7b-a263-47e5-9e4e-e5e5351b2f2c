{"name": "hospital_management_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "start:css": "tailwindcss -i ./src/input.css -o ./dist/output.css --watch", "build:css": "tailwindcss -i ./src/input.css -o ./dist/output.css --minify", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.6.1", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "@types/react-router-dom": "^5.3.3", "axios": "^1.8.3", "dayjs": "^1.11.13", "jwt-decode": "^4.0.0", "lucide-react": "^0.487.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^6.30.0", "react-webcam": "^7.2.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/node": "^22.15.3", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "tailwindcss": "^3.4.17", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}