import React, { useEffect } from "react";
import View from "@/components/view";
import Input from "@/components/input";
import { useSelector } from "react-redux";
import Textarea from "@/components/Textarea";
import { UserInterface } from "@/interfaces/users";
import useForm from "@/utils/custom-hooks/use-form";
import CountryStateDropdown from "@/components/countryStatedropdown";
import Select from "@/components/Select";
import { useUsers } from "@/actions/calls/user";
import { useDepartment } from "@/actions/calls/department";

interface SectionTwoProps {
  formType: "add" | "edit";
  errorsAddress: string | undefined;
  errorsCity: string | undefined;
  errorsState: string | undefined;
  errorsCountry: string | undefined;
  errorsPinCode: string | undefined;
  errorsDesignation: string | undefined;
  errorsQualification: string | undefined;
  errorsRole: string | undefined;
  errorsDepartment: string | undefined;
}

const SectionTwo: React.FC<SectionTwoProps> = ({
  formType,
  errorsAddress,
  errorsCity,
  errorsRole,
  errorsState,
  errorsCountry,
  errorsPinCode,
  errorsDesignation,
  errorsQualification,
  errorsDepartment,
}) => {
  const { rolesList } = useUsers();
  const { departmentDropdownHandler } = useDepartment();
  const roles = useSelector((state: any) => state?.users?.rolesList);
  const userDetails = useSelector((state: any) => state?.users?.userDetails);
  const department = useSelector((state: any) => state?.department?.departmentDropdownData);
  const departmentObj = department?.map((department: any) => ({
    id: department?.id,
    label: department?.name,
    value: department?.id,
  }));
  const { values, handleChange } = useForm<UserInterface>(userDetails);

  useEffect(() => {
    rolesList(() => {});
    departmentDropdownHandler(() => {});
  }, []);

  return (
    <React.Fragment>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
        <View className="col-span-2">
          <Textarea
            id="address"
            label="Address"
            name="address"
            placeholder="Ex: 123 Main St, Anytown, CA 12345"
            error={errorsAddress}
            value={values?.address ?? ""}
            onChange={handleChange}
          />
        </View>

        <CountryStateDropdown
          cityName="city"
          stateName="state"
          formType={formType}
          countryName="country"
          cityValue={values?.city}
          stateValue={values?.state}
          countryValue={values?.country}
          errorsCity={errorsCity}
          errorsState={errorsState}
          errorsCountry={errorsCountry}
        />

        <View>
          <Input
            // id="address"
            label="Pin Code"
            name="pincode"
            type="text"
            className={`w-full`}
            placeholder="Ex: 123456"
            error={errorsPinCode}
            value={values?.pincode}
            onChange={handleChange}
          />
        </View>

        <View className="col-span-2">
          <h3 className="text-lg font-semibold text-text-DEFAULT mb-3 border-b border-neutral-200 pb-2">
            Professional Information
          </h3>
        </View>

        {/* Designation */}

        <View>
          <Input
            id="designation"
            label="Designation"
            name="designation"
            onChange={handleChange}
            placeholder="Ex: Doctor"
            error={errorsDesignation}
            value={values?.designation}
          />
        </View>

        {/* Qualification */}
        <View>
          <Input
            id="qualification"
            label="Qualification"
            name="qualification"
            onChange={handleChange}
            placeholder="Ex: MBBS"
            error={errorsQualification}
            value={values?.qualification}
          />
        </View>

        <View>
          <Select 
            id="department"
            label="Department"
            name="department"
            value={values?.department}
            onChange={handleChange}
            options={departmentObj}
            placeholder="Select Department"
            error={errorsDepartment}
            required={true}
          />
        </View>

        <View>
          <Select
            id="role"
            label="Role"
            name="role"
            options={roles?.map((role: any) => ({
              value: role?.name,
              label: role?.name?.charAt(0).toUpperCase() + role?.name?.slice(1),
            }))}
            onChange={handleChange}
            placeholder="Select Role"
            error={errorsRole}
            value={values?.role}
            required={true}
          />
        </View>
      </View>
    </React.Fragment>
  );
};

export default SectionTwo;
