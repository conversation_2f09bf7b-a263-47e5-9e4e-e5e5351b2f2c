import View from "@/components/view";
import Text from "@/components/text";
import SectionOne from "./SectionOne";
import LaunchApi from "@/actions/api";
import Input from "@/components/input";
import Select from "@/components/Select";
import Button from "@/components/button";
import { RootState } from "@/actions/store";
import { useParams } from "react-router-dom";
import React, { useEffect, useState } from "react";
import { useInvoice } from "@/actions/calls/invoice";
import { useDispatch, useSelector } from "react-redux";
import { INVOICE_ADD_OR_UPDATE_URL } from "@/utils/urls/backend";
import { clearYogaAsanaDetailSlice } from "@/actions/slices/yogaAsana";
import { paymentTypeOptions } from "../forms/appointmentsForm/appointmentFormOptions";
import { toast } from "@/utils/custom-hooks/use-toast";
import PaymentSection from "./PaymentSection";
// import { paymentStatusOptions } from "../forms/patientForm/patientFormOptions";

const api = new LaunchApi();

const InvoiceDetail: React.FC = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const [amount, setAmount] = useState<number>(0);
  const [taxAmount, ] = useState<number>(0);
  const [type, setType] = useState<boolean>(false);
  // const [testTotalAmountData, setTestTotalAmountData] = useState<number>(0);
  const { getPaymentDetailHandler, getInvoiceDetailHandler, cleanUp } =
    useInvoice();
  const [paymentType, setPaymentType] = useState<string>("");
  const [transactionId, setTransactionId] = useState<string>("");
  const [paymentStatus, setPaymentStatus] = useState<string>("");
  const invoiceData = useSelector(
    (state: RootState) => state.invoice.invoiceDetailData
  );

  useEffect(() => {
    if (id) {
      getPaymentDetailHandler(id, () => {});
      getInvoiceDetailHandler(id, () => {});
    }
    return () => {
      cleanUp();
      dispatch(clearYogaAsanaDetailSlice());
    };
  }, [id]);

  useEffect(() => {
    setPaymentStatus(invoiceData?.payment_status);
    setAmount(invoiceData?.collected_amount);
    setPaymentType(invoiceData?.payment_type);
    setType(invoiceData?.type !== "Follow-up" ? true : false);
    setTransactionId(invoiceData?.transaction_id);
  }, [
    invoiceData?.type,
    invoiceData?.payment_type,
    invoiceData?.transaction_id,
    invoiceData?.payment_status,
    invoiceData?.collected_amount,
  ]);

  const submitData = () => {
    // let calculatedAmountData;
    // if (type) {
    //   calculatedAmountData =
    //     invoiceData?.enroll_fees +
    //     invoiceData?.paymentArray?.amount +
    //     testTotalAmountData;
    // } else {
    //   calculatedAmountData =
    //     invoiceData?.paymentArray?.amount + testTotalAmountData;
    // }

    api.post(
      `${INVOICE_ADD_OR_UPDATE_URL}/${invoiceData?.id}`,
      (_, success, status) => {
        if (success && status === 200) {
          toast({
            title: "Success!",
            description: "Successfully Amount added",
            variant: "success",
          });
          if (id) {
            getInvoiceDetailHandler(id, () => {});
          }
        }
      },
      {
        // invoice_id: invoiceData?.id,
        tax_amount: taxAmount,
        collected_amount: amount,
        columnName: "consultation_id",
        consultationId: invoiceData?.id,
        payment_type: paymentType,
        transaction_id: transactionId,
        paymentStatus: paymentStatus,
        balanced_amount: invoiceData?.total_amount - amount,
      }
    );
  };

  return (
    <View className="min-h-screen p-8">
      <View className="max-w-4xl mx-auto">
        {/* <SectionOne type={type} testTotalAmountData={testTotalAmountData} /> */}
        <SectionOne
          type={type}
          balanceAmount={
            <>
              {!!invoiceData?.balanced_amount && (
                <Text className="text-muted-foreground text-sm">
                  Balance Amount : ₹{invoiceData?.balanced_amount}
                </Text>
              )}
            </>
          }
        />
        <hr style={{ margin: "20px 0px" }} />
        <View className="mt-8">
          <Text as="h2">Add Amount</Text>
          <PaymentSection />
        </View>
        <hr style={{ margin: "20px 0px" }} />
        <View>
          <Text as="h2">Collect Amount</Text>
          <form>
            <View className="mt-4">
              <Input
                type="number"
                label="Collected Amount"
                name="collected_amount"
                value={amount}
                placeholder="Enter Collected Amount"
                onChange={(e) => {
                  setAmount(Number(e.target.value));
                }}
              />
            </View>
            {/* <View className="mt-4">
            <Input
              label="Tax Amount"
              type="number"
              name="tax_amount"
              value={amount}
              placeholder="Enter Tax Amount"
              onChange={(e) => {
                setTaxAmount(Number(e.target.value));
              }}
            />
          </View> */}
            <View className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {/* Mode of Payment Dropdown */}
              <View>
                <Text className="text-sm font-medium text-gray-700 dark:text-white mb-1">
                  Mode of Payment
                </Text>
                <Select
                  name="payment_type"
                  options={paymentTypeOptions}
                  placeholder="Select Payment Method"
                  value={paymentType}
                  onChange={(e: any) => {
                    setPaymentType(e.target.value);
                  }}
                />
              </View>

              {/* Transaction ID Input */}
              <View>
                <Text className="text-sm font-medium text-gray-700 dark:text-white mb-1">
                  Transaction ID
                </Text>
                <Input
                  name="transaction_id"
                  placeholder="Enter Transaction ID"
                  value={transactionId}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setTransactionId(e.target.value);
                  }}
                />
              </View>
            </View>
            <View className="w-full my-4">
              <Select
                label="Payment Status"
                value={paymentStatus}
                onChange={(e) => {
                  setPaymentStatus(e.target.value);
                  // onSetHandler("payment_status", e.target.value)
                }}
                placeholder="Select Appointment Payment Status"
                options={[
                  { label: "Pending", value: "Pending" },
                  { label: "Completed", value: "Completed" },
                ]}
                // error={errorsPaymentStatus}
              />
            </View>
            <View className=" flex justify-end">
              <Button
                type="button"
                className="mt-4"
                onClick={() => {
                  if (!paymentType && !transactionId && !amount) {
                    return alert(
                      "Please select payment type and transaction id"
                    );
                  }

                  if (amount < invoiceData?.total_amount) {
                    const confirmed = confirm(
                      "Amount is less than total. Do you want to continue?"
                    );
                    if (confirmed) {
                      submitData();
                      return;
                    }
                  }
                  if (amount > invoiceData?.total_amount) {
                    return alert("Amount is more than total amount");
                  }
                  submitData();

                  // let calculatedAmountData;
                  // if (type) {
                  //   calculatedAmountData =
                  //     invoiceData?.enroll_fees +
                  //     invoiceData?.paymentArray?.amount +
                  //     testTotalAmountData;
                  // } else {
                  //   calculatedAmountData =
                  //     invoiceData?.paymentArray?.amount + testTotalAmountData;
                  // }
                  // // setTestTotalAmountData(calculatedAmountData);
                  // if (amount < calculatedAmountData) {
                  //   const confirmed = confirm(
                  //     "Amount is less than total. Do you want to continue?"
                  //   );
                  //   if (confirmed) {
                  //     submitData();
                  //   }
                  // }
                  // if (amount > calculatedAmountData) {
                  //   return alert("Amount is more than total amount");
                  // }
                  // submitData();
                }}
              >
                Submit
              </Button>
            </View>
          </form>
        </View>
      </View>
      {/* Footer */}
      <View className="mt-12 pt-4 border-t border-gray-300">
        <Text as="p" className="text-center text-gray-500 text-sm">
          {import.meta.env.VITE_HOSPITAL_NAME || "MedCare Hospital"}
        </Text>
      </View>
    </View>
  );
};

export default InvoiceDetail;
