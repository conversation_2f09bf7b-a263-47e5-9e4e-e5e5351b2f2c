import Button from "@/components/button";
import Text from "@/components/text";
import View from "@/components/view";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
// import SectionOne from "./SectionOne";
import { validationForm } from "./validationForm";
import { toast } from "@/utils/custom-hooks/use-toast";
import { FormTypeProps } from "@/interfaces/dashboard";

import { useConsultationFees } from "@/actions/calls/consultationFees";
import { ConsultationFees } from "@/interfaces/master/consultatoin fees(cost)";
import SingleSelector from "@/components/SingleSelector";
import Input from "@/components/input";
import useForm from "@/utils/custom-hooks/use-form";
import { useDispatch, useSelector } from "react-redux";
import { statusOptions } from "./consultationFormOptions";
import { clearConsultationFeesDetailSlice } from "@/actions/slices/consultationFees";

const ConsultationFeesForm: React.FC<FormTypeProps> = ({ formType = "add" }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const dispatch = useDispatch();

  const [isSubmitting, setIsSubmitting] = useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});

  const { addConsultationFeesHandler, editConsultationFeesHandler, consultationFeesDetailHandler, cleanUp } = useConsultationFees();
  const consultationFeesData = useSelector(
    (state: any) => state?.consultationFees?.consultationFeesDetailData
  );
    const { values, handleChange, onSetHandler } = useForm<ConsultationFees | null>(consultationFeesData);

     useEffect(() => {
        if (!id && formType === "edit") {
          navigate(-1);
          return;
        }
      }, [id, formType]);
      useEffect(() => {
        if (id) {
                consultationFeesDetailHandler(id, () => {});
        }
        return () => {
          cleanUp();
          dispatch(clearConsultationFeesDetailSlice());

        };
      }, [id]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const consultationFeesFormObj: Partial<ConsultationFees> = {};

    try {
      for (let [key, value] of formData.entries()) {

        consultationFeesFormObj[key as keyof ConsultationFees] = value as any;
      }
      await validationForm.validate(consultationFeesFormObj, { abortEarly: false });
      setErrors({});
      setIsSubmitting(true);
      if (formType === "add") {
        addConsultationFeesHandler(consultationFeesFormObj, (success, response) => {
          setIsSubmitting(false);
          if (success) {
            toast({
              title: "Success!",
              description: "The consultation fees added successfully.",
              variant: "default",
            });
            navigate(-1);
          } else {
            toast({
              title: "Error!",
              description: response?.message,
              variant: "destructive",
            });
          }
        });
      } else if (id) {
        editConsultationFeesHandler(id, consultationFeesFormObj, (success: boolean) => {
          if (success) {
            navigate(-1);
            toast({
              title: "Success!",
              description: "Consultation Fees Updated successfully.",
              variant: "success",
            });
          } else {
            setIsSubmitting(false);
            toast({
              title: "Error!",
              description: "Failed to update Consultation Fees",
              variant: "destructive",
            });
          }
          setIsSubmitting(false);
        });
      }
    } catch (error: any) {
      setIsSubmitting(false);
      if (error.inner) {
        const validationErrors: Record<string, string> = {};
        error.inner.forEach((e: any) => {
          validationErrors[e.path] = e.message;
        });
        setErrors(validationErrors);
      }
    }
  };
  return (
    <View className="min-h-screen dark:bg-background flex flex-col  items-center p-4">
      <View className="border border-border bg-white dark:bg-card rounded-lg shadow-card w-full max-w-4xl p-6 md:p-8 mb-8">
        <View className=" flex items-center justify-between">
          <Text
            as="h2"
            weight="font-bold"
            className="text-2xl font-bold text-center text-primary mb-2"
          >
            {formType === "add" ? "Add Consultation Fees" : "Edit Consultation Fees"}
          </Text>
          <Button onPress={() => navigate(-1)} variant="outline">
            Back to Home
          </Button>
        </View>
        <Text as="p" className="text-text-light text-left mb-6">
          {/* {formType === "add" && "Fill in the details to create a new account"} */}
          Fill the consultation fees details
        </Text>
        <form onSubmit={handleSubmit}>
          <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <View>
        <Input
          id="amount"
          name="amount"
          required={true}
          error={errors?.amount}
          label="Consultation Amount"
          value={values?.amount || ""}
          onChange={handleChange}
          placeholder="Enter Finding Name"
        />
      </View>
      <View>
        <SingleSelector
          id="status"
          label="Status"
          name="status"
          error={errors?.status}
          value={values?.status || statusOptions[0].value}
          placeholder="Select Status"
          onChange={(value) => {
            onSetHandler("status", value);
          }}
          options={statusOptions}
          required={true}
        />
      </View>
    </View>

          <View className="col-span-2 mt-6">
            <Button
              htmlType="submit"
              loading={isSubmitting}
              onPress={() => handleSubmit}
              className="w-full bg-primary text-white rounded-md py-3 font-medium hover:bg-primary-600 transition focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-2"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </View>
        </form>
      </View>
    </View>
  );
};
export default ConsultationFeesForm;
