// Input component
import { InputProps } from "@/interfaces/components/input/input";
import View from "./view";

interface CSSProps {
  small: string;
  medium: string;
  large: string;
}
interface VariantProps {
  default: string;
  filled: string;
  error: string;
  outlined: string;
}

const sizeClasses: CSSProps = {
  small: "h-8 text-xs px-2 py-1",
  medium: "h-10 text-sm px-3 py-2",
  large: "h-12 text-base px-4 py-3",
};

const setVariantHandler: VariantProps = {
  outlined: "",
  error: "border-danger focus:ring-danger/30",
  default:
    "w-full p-3 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300 transition",
  filled:
    "w-full p-3 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300 transition bg-primary",
};

const Input: React.FC<InputProps> = ({
  id,
  ref,
  name,
  style,
  error,
  value,
  label,
  onBlur,
  onChange,
  leftIcon,
  rightIcon,
  className,
  type = "text",
  disabled = false,
  fullWidth = false,
  variant = "default",
  inputSize = "medium",
  placeholder = "Enter text",
  required = false,
  ...rest
}) => {
  return (
    <View className="relative w-full">
      {label && (
        <label htmlFor={name}>
          {label}
          {required && <span className="text-red-600 ">*</span>}
        </label>
      )}
      <View className={`input-wrapper ${fullWidth ? "w-full" : ""}`}>
        {leftIcon && <View className="input-icon-left">{leftIcon}</View>}
        <input
          id={id}
          ref={ref}
          name={name}
          type={type}
          value={value?.toString()}
          style={style}
          onBlur={onBlur}
          autoComplete="off"
          onChange={onChange}
          disabled={disabled}
          placeholder={placeholder}
          className={`w-full bg-transparent p-3 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-300 transition ${
            setVariantHandler[variant]
          } input-${variant}  input-${sizeClasses[inputSize]} ${
            error && "border-red-500"
          } ${className || ""}`}
          {...rest}
        />
        {rightIcon && <div className="input-icon-right">{rightIcon}</div>}
      </View>
      {error && <p className="text-red-500 text-sm">{error}</p>}
    </View>
  );
};

export default Input;
