import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import Input from "@/components/input";
import View from "@/components/view";
import Text from "@/components/text";
import Switch from "@/components/ui/switch";
import useForm from "@/utils/custom-hooks/use-form";
import { Consultation } from "@/interfaces/consultation";
import InfoCard from "@/components/ui/infoCard";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { Calendar, User } from "lucide-react";
import SingleSelector from "@/components/SingleSelector";
import TransferList, { TransferListItem } from "@/components/TransferList";
import { useChiefComplaint } from "@/actions/calls/chiefComplaints";

const SectionTwo: React.FC = () => {
  const consultationDetail = useSelector(
    (state: RootState) =>
      state.consultation.consultationDetailData?.consultations
  );
  const { values, handleC<PERSON><PERSON>, handleTipTap<PERSON><PERSON>e, onSetHand<PERSON> } =
    useForm<Consultation | null>(consultationDetail);

const {chiefComplaintDropdownHandler} = useChiefComplaint();

const chiefComplaintData = useSelector(
    (state: RootState) => state.chiefComplaint.chiefComplaintDropdownData
  );

const chiefComplaintObj = chiefComplaintData?.map((item: any) => ({
    id: item?.id,
    label: item?.complaint_name,
    value: item?.complaint_name,
  }));

  return (
    <>
      <Card className="mt-2">   
        {/* chief complaints */}
          <View>
             <TransferList
              name="chief_complaint"
              label="Chief Complaints"
              sourceData={chiefComplaintObj}
              selectedItems={values?.complaint_name || []}
              onSelectionChange={(value: TransferListItem[]) => {
                onSetHandler("complaint_name", value);
              }}
              placeholder="Search complaints..."
              sourceTitle="Available Complaints"
              selectedTitle="Selected Complaints"
              height="150px"
              searchable
              showCount
              allowSelectAll
              allowCustomValues
              customValuePlaceholder="Add custom complaint"
            />
          </View>

          {/* surgical history  */}
          <View className="mt-6">
            <TransferList
              name="surgical_history"
              label="Surgical History"
              sourceData={[
                { id: 1, label: "Appendectomy", value: "appendectomy" },
                { id: 2, label: "Hysterectomy", value: "hysterectomy" },
                { id: 3, label: "Cesarean Section", value: "cesarean_section" },
                { id: 4, label: "Laparoscopy", value: "laparoscopy" },
              ]}
              selectedItems={[]}
              onSelectionChange={(value) => {
                console.log("value", value);
              }}
              placeholder="Search surgical history..."
              sourceTitle="Available Surgical History"
              selectedTitle="Selected Surgical History"
              height="150px"
              searchable
              showCount
              allowSelectAll
              allowCustomValues
              customValuePlaceholder="Add custom surgical history"
            />
          </View>

          {/* Co-morbidities */}
          <View className="mt-6">
            <TransferList
              name="co_morbidities"
              label="Co-morbidities"
              sourceData={[
                { id: 1, label: "Diabetes", value: "diabetes" },
                { id: 2, label: "Hypertension", value: "hypertension" },
                { id: 3, label: "Asthma", value: "asthma" },
                { id: 4, label: "Arthritis", value: "arthritis" },
              ]}
              selectedItems={[]}
              onSelectionChange={(value) => {
                console.log("value", value);
              }}
              placeholder="Search co-morbidities..."
              sourceTitle="Available Co-morbidities"
              selectedTitle="Selected Co-morbidities"
              height="150px"
              maxSelections={6}
              searchable
              showCount
              allowSelectAll
              allowCustomValues
              customValuePlaceholder="Add custom co-morbidity"
            />
          </View>
      </Card>
    </>
  )
}

export default SectionTwo;
        