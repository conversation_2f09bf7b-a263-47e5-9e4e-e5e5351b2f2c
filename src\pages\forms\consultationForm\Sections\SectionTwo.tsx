import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import Input from "@/components/input";
import View from "@/components/view";
import Text from "@/components/text";
import Switch from "@/components/ui/switch";
import useForm from "@/utils/custom-hooks/use-form";
import { Consultation } from "@/interfaces/consultation";
import InfoCard from "@/components/ui/infoCard";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { Calendar, User } from "lucide-react";
import SingleSelector from "@/components/SingleSelector";
import TransferList, { TransferListItem } from "@/components/TransferList";
import { useChiefComplaint } from "@/actions/calls/chiefComplaints";
import { useSurgicalHistory } from "@/actions/calls/surgicalHistory";
import { useComorbidity } from "@/actions/calls/comorbidities";
import Textarea from "@/components/Textarea";

const SectionTwo: React.FC = () => {
  const [isChronic, setIsChronic] = useState(false);
  const [description, setDescription] = useState("");
  const [comorbiditiesData, setComorbiditiesData] = useState<any>([]);
  const consultationDetail = useSelector(
    (state: RootState) =>
      state.consultation.consultationDetailData?.consultations
  );
  const { values, handleChange, handleTipTapChange, onSetHandler } =
    useForm<Consultation | null>(consultationDetail);

const {chiefComplaintDropdownHandler} = useChiefComplaint();
const {surgicalHistoryDropdownHandler} = useSurgicalHistory();
const { comorbidityDropdownHandler } = useComorbidity();

const chiefComplaintData = useSelector(
    (state: RootState) => state.chiefComplaint.chiefComplaintDropdownData
  );

const surgicalHistoryData = useSelector(
    (state: RootState) => state.surgicalHistory.surgicalHistoryDropdownData
  );

  const comorbidityData = useSelector(
    (state: RootState) => state.comorbidities.comorbidityDropdown
  );

const chiefComplaintObj = chiefComplaintData?.map((item: any) => ({
    id: item?.id,
    label: item?.complaint_name,
    value: item?.complaint_name,
  }));

const surgicalHistoryObj = surgicalHistoryData?.map((item: any) => ({
    id: item?.id,
    label: item?.surgery_name,
    value: item?.surgery_name,
  }));

  const comorbidityObj = comorbidityData? comorbidityData?.map((item: any) => ({
    id: item?.id,
    label: item?.name,
    value: item?.name,
  })) : [];


  useEffect(() => {
    chiefComplaintDropdownHandler(() => {});
    surgicalHistoryDropdownHandler(() => {});
    comorbidityDropdownHandler(() => {});
  }, []);

  return (
    <>
      <Card className="mt-2">   
        {/* chief complaints */}
          <View>
             <TransferList
              name="chief_complaint"
              label="Chief Complaints"
              sourceData={chiefComplaintObj}
              selectedItems={values?.complaint_name || []}
              onSelectionChange={(value: TransferListItem[]) => {
                onSetHandler("complaint_name", value);
              }}
              placeholder="Search complaints..."
              sourceTitle="Available Complaints"
              selectedTitle="Selected Complaints"
              height="150px"
              searchable
              showCount
              allowSelectAll
              // allowCustomValues
              // customValuePlaceholder="Add custom complaint"
            />
          </View>

          {/* surgical history  */}
          <View className="mt-6">
            <TransferList
              name="surgical_history"
              label="Surgical History"
              sourceData={surgicalHistoryObj}
              selectedItems={values?.surgical_history || []}
              onSelectionChange={(value) => {
                onSetHandler("surgical_history", value);
              }}
              placeholder="Search surgical history..."
              sourceTitle="Available Surgical History"
              selectedTitle="Selected Surgical History"
              height="150px"
              searchable
              showCount
              allowSelectAll
            />
          </View>

          {/* Co-morbidities */}
          <View className="mt-6">
            <TransferList
              name="co_morbidities"
              label="Co-morbidities"
              sourceData={comorbidityObj}
              selectedItems={values?.co_morbidities || []}
              onSelectionChange={(value) => {
                onSetHandler("co_morbidities", value);
              }}
              placeholder="Search co-morbidities..."
              sourceTitle="Available Co-morbidities"
              selectedTitle="Selected Co-morbidities"
              height="150px"
              // maxSelections={6}
              searchable
              showCount
              allowSelectAll
              // allowCustomValues
              // customValuePlaceholder="Add custom co-morbidity"
            />
          </View>

          {
            values?.co_morbidities ? (
                <>
                  {values?.co_morbidities.map((item: any) => (
                    <View key={item?.id}>
                      <Card className="mt-2 !bg-background p-4">
                         <View>
                            <Text as="h3" weight="font-bold" className=" pb-2 mb-2">
                              {item?.label}
                            </Text>
                            <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              <View>
                              <SingleSelector 
                              id={item?.id}
                              name={item?.id}
                              label="Is Chronic"
                              value={isChronic || false}
                              placeholder="Select Is Chronic"
                              onChange={(value) => {
                                onSetHandler("is_chronic", value);
                              }}
                              options={[
                                { label: "Yes", value: true },
                                { label: "No", value: false },
                              ]}
                            />
                            </View>
                            <View className="col-span-2">
                                <Textarea
                                id={item?.id}
                                // name={item?.id}
                                label="Description"
                                onChange={handleChange}
                                value={description || ""}
                                placeholder="Enter Description"
                              />
                            </View>
                            </View>
                         </View>
                      </Card>
                    </View>
                  ))}
                </>
            ) : null
          }
      </Card>
    </>
  )
}

export default SectionTwo;
        