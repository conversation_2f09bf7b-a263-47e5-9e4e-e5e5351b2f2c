import { ConsultationState } from "@/interfaces/slices/consultation";
import { createSlice } from "@reduxjs/toolkit";

const initialState: ConsultationState = {
  consultationDetailData: {},
  consultationListData: [],
  consultationDropdownData: [],
  loading: false,
};

const consultationSlice = createSlice({
  name: "consultation",
  initialState,
  reducers: {
    consultationListSlice: (state, action) => {
      state.consultationListData = action?.payload;
      state.loading = false;
    },
    consultationDetailSlice: (state, action) => {
      state.consultationDetailData = action?.payload;
      state.loading = false;
    },
    consultationDropdownSlice: (state, action) => {
      state.consultationDropdownData = action?.payload;
      state.loading = false;
    },
    clearConsultationDetailSlice: (state) => {
      state.consultationDetailData = null;
    },
  },
});

export const {
  consultationDetailSlice,
  consultationListSlice,
  clearConsultationDetailSlice,
  consultationDropdownSlice,
} = consultationSlice.actions;
export default consultationSlice.reducer;
