import React, { useState } from 'react';
import TransferList from './TransferList';
import View from './view';
import Text from './text';

const TransferListExample: React.FC = () => {
  const [selectedStaff, setSelectedStaff] = useState<any[]>([]);
  const [selectedDepartments, setSelectedDepartments] = useState<any[]>([]);

  // Hospital staff data
  const hospitalStaff = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      email: '<EMAIL>',
      department: 'Cardiology',
      role: 'doctor',
      specialization: 'Interventional Cardiology',
      status: 'active'
    },
    {
      id: 2,
      name: 'Nurse <PERSON>',
      email: '<EMAIL>',
      department: 'Emergency',
      role: 'nurse',
      status: 'active'
    },
    {
      id: 3,
      name: 'Dr. <PERSON>',
      email: '<EMAIL>',
      department: 'Neurology',
      role: 'doctor',
      specialization: 'Neurosurgery',
      status: 'on-leave'
    },
    {
      id: 4,
      name: 'Nurse <PERSON>',
      email: 'robert.wils<PERSON>@hospital.com',
      department: 'ICU',
      role: 'nurse',
      status: 'active'
    },
    {
      id: 5,
      name: 'Dr. <PERSON>',
      email: 'lisa.and<PERSON>@hospital.com',
      department: 'Pediatrics',
      role: 'doctor',
      specialization: 'Pediatric Surgery',
      status: 'active'
    },
    {
      id: 6,
      name: 'Technician James Brown',
      email: '<EMAIL>',
      department: 'Radiology',
      role: 'technician',
      status: 'inactive'
    }
  ];

  const departments = [
    {
      id: 'cardiology',
      name: 'Cardiology Department',
      department: 'Cardiology',
      status: 'active'
    },
    {
      id: 'emergency',
      name: 'Emergency Department',
      department: 'Emergency',
      status: 'active'
    },
    {
      id: 'neurology',
      name: 'Neurology Department',
      department: 'Neurology',
      status: 'active'
    },
    {
      id: 'icu',
      name: 'Intensive Care Unit',
      department: 'ICU',
      status: 'active'
    },
    {
      id: 'pediatrics',
      name: 'Pediatrics Department',
      department: 'Pediatrics',
      status: 'active'
    },
    {
      id: 'radiology',
      name: 'Radiology Department',
      department: 'Radiology',
      status: 'active'
    }
  ];

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', {
      selectedStaff: selectedStaff,
      selectedDepartments: selectedDepartments
    });
    alert('Check console for form data');
  };

  return (
    <View className="min-h-screen bg-background py-8">
      <View className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <View className="text-center mb-8">
          <Text as="h1" className="text-4xl font-bold text-foreground mb-4">
            Hospital Management TransferList
          </Text>
          <Text as="p" className="text-lg text-muted-foreground max-w-3xl mx-auto">
            A modern, accessible dual-list selector designed for hospital management systems.
            Perfect for assigning staff, managing departments, and organizing resources.
          </Text>
        </View>

        {/* Staff Assignment Example */}
        <View className="bg-card rounded-xl shadow-sm border border-border p-6 mb-8">
          <Text as="h2" className="text-2xl font-semibold text-foreground mb-4">Staff Assignment</Text>
          <Text as="p" className="text-muted-foreground mb-6">
            Assign hospital staff to specific departments or projects with detailed information display.
          </Text>

          <TransferList
            label="Assign Staff Members"
            sourceData={hospitalStaff}
            selectedItems={selectedStaff}
            onSelectionChange={setSelectedStaff}
            sourceTitle="Available Staff"
            selectedTitle="Assigned Staff"
            height="350px"
            searchable={true}
            showCount={true}
            allowSelectAll={true}
            maxSelections={5}
            placeholder="Search staff by name, email, or department..."
            className="mb-4"
          />

          <View className="mt-4 p-4 bg-muted rounded-lg">
            <Text as="h4" className="font-medium text-foreground mb-2">Selected Staff:</Text>
            <Text as="p" className="text-sm text-muted-foreground">
              {selectedStaff.length > 0
                ? selectedStaff.map(staff => staff.name).join(', ')
                : 'No staff members selected'
              }
            </Text>
          </View>
        </View>

        {/* Department Management Example */}
        <View className="bg-card rounded-xl shadow-sm border border-border p-6 mb-8">
          <Text as="h2" className="text-2xl font-semibold text-foreground mb-4">Department Management</Text>
          <Text as="p" className="text-muted-foreground mb-6">
            Manage hospital departments with form integration and validation.
          </Text>

          <form onSubmit={handleFormSubmit} className="space-y-6">
            <TransferList
              label="Select Departments"
              sourceData={departments}
              selectedItems={selectedDepartments}
              onSelectionChange={setSelectedDepartments}
              sourceTitle="Available Departments"
              selectedTitle="Active Departments"
              height="250px"
              searchable={true}
              showCount={true}
              allowSelectAll={true}
              placeholder="Search departments..."
              required={true}
            />

            <button
              type="submit"
              className="w-full bg-primary hover:bg-primary-600 text-primary-foreground font-medium py-3 px-4 rounded-lg transition-colors duration-200"
            >
              Update Department Configuration
            </button>
          </form>
        </View>

        {/* Usage Information */}
        <View className="bg-card rounded-xl shadow-sm border border-border p-6">
          <Text as="h2" className="text-2xl font-semibold text-foreground mb-4">Usage Information</Text>
          <View className="space-y-4 text-sm text-muted-foreground">
            <Text as="p">• Click items to select/deselect them</Text>
            <Text as="p">• Use the control buttons to move items between lists</Text>
            <Text as="p">• Search functionality works across all item properties</Text>
            <Text as="p">• Keyboard navigation supported with arrow keys</Text>
            <Text as="p">• Fully accessible with screen reader support</Text>
            <Text as="p">• Responsive design adapts to different screen sizes</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default TransferListExample;
