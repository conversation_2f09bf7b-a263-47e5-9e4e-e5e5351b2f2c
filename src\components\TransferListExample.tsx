import React, { useState } from 'react';
import TransferList from './TransferList';

const TransferListExample: React.FC = () => {
  const [selectedValues, setSelectedValues] = useState<(string | number)[]>(['option2', 'option5']);
  const [formData, setFormData] = useState({
    skills: [] as (string | number)[],
    departments: [] as (string | number)[],
  });

  // Sample data
  const skillOptions = [
    { value: 'javascript', label: 'JavaScript', description: 'Modern web development language' },
    { value: 'react', label: 'React', description: 'Popular frontend framework' },
    { value: 'nodejs', label: 'Node.js', description: 'Server-side JavaScript runtime' },
    { value: 'python', label: 'Python', description: 'Versatile programming language' },
    { value: 'typescript', label: 'TypeScript', description: 'Typed superset of JavaScript' },
    { value: 'vue', label: 'Vue.js', description: 'Progressive frontend framework' },
    { value: 'angular', label: 'Angular', description: 'Full-featured frontend framework' },
    { value: 'css', label: 'CSS', description: 'Styling language for web pages' },
    { value: 'html', label: 'HTML', description: 'Markup language for web content' },
    { value: 'sql', label: 'SQL', description: 'Database query language' },
    { value: 'git', label: 'Git', description: 'Version control system' },
    { value: 'docker', label: 'Docker', description: 'Containerization platform', disabled: true },
  ];

  const departmentOptions = [
    { value: 'engineering', label: 'Engineering', description: 'Software development and technical roles' },
    { value: 'marketing', label: 'Marketing', description: 'Brand promotion and customer acquisition' },
    { value: 'sales', label: 'Sales', description: 'Revenue generation and client relations' },
    { value: 'hr', label: 'Human Resources', description: 'Employee management and recruitment' },
    { value: 'finance', label: 'Finance', description: 'Financial planning and accounting' },
    { value: 'operations', label: 'Operations', description: 'Business process management' },
  ];

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', { ...formData, basicSelection: selectedValues });
    alert('Check console for form data');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Modern TransferList Component
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            A premium, accessible, and feature-rich dual-list selector built with React and Tailwind CSS.
            Perfect for forms, user preferences, and data management interfaces.
          </p>
        </div>

        {/* Features Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Smart Search</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">Real-time filtering with label and description matching</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Accessible</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">Full keyboard navigation and screen reader support</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Responsive</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">Adapts beautifully to all screen sizes and devices</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
            <div className="w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex items-center justify-center mb-4">
              <svg className="w-6 h-6 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Customizable</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">Extensive props for styling and behavior control</p>
          </div>
        </div>

        {/* Basic Example */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Basic Example</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            A simple transfer list with search functionality and all standard controls.
          </p>
          
          <TransferList
            label="Select Your Skills"
            options={skillOptions}
            value={selectedValues}
            onChange={setSelectedValues}
            availableTitle="Available Skills"
            selectedTitle="Selected Skills"
            height="250px"
            searchable={true}
            showSelectAll={true}
            showItemCount={true}
            className="mb-4"
          />

          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Selected Values:</h4>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              {selectedValues.length > 0 ? selectedValues.join(', ') : 'No items selected'}
            </p>
          </div>
        </div>

        {/* Form Integration Example */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Form Integration</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Demonstrates how to integrate the TransferList component with forms and validation.
          </p>

          <form onSubmit={handleFormSubmit} className="space-y-6">
            <TransferList
              label="Required Skills"
              options={skillOptions.slice(0, 8)}
              value={formData.skills}
              onChange={(values) => setFormData(prev => ({ ...prev, skills: values }))}
              availableTitle="Available Skills"
              selectedTitle="Required Skills"
              height="200px"
              maxSelections={5}
              required={true}
              name="skills"
              id="skills"
            />

            <TransferList
              label="Departments"
              options={departmentOptions}
              value={formData.departments}
              onChange={(values) => setFormData(prev => ({ ...prev, departments: values }))}
              availableTitle="All Departments"
              selectedTitle="Selected Departments"
              height="180px"
              compact={true}
              name="departments"
              id="departments"
            />

            <button
              type="submit"
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200"
            >
              Submit Form
            </button>
          </form>
        </div>

        {/* Compact Example */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">Compact Mode</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            A space-efficient version perfect for smaller interfaces or sidebars.
          </p>

          <TransferList
            options={departmentOptions}
            value={[]}
            onChange={() => {}}
            availableTitle="Available"
            selectedTitle="Selected"
            height="150px"
            compact={true}
            showSelectAll={false}
            searchable={false}
          />
        </div>
      </div>
    </div>
  );
};

export default TransferListExample;
