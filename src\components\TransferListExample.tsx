import React, { useState } from 'react';
import TransferList from './TransferList';
import View from './view';
import Text from './text';

const TransferListExample: React.FC = () => {
  const [selectedStaff, setSelectedStaff] = useState<any[]>([]);
  const [selectedDepartments, setSelectedDepartments] = useState<any[]>([]);

  // Sample staff data using generic interface
  const hospitalStaff = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      label: 'Dr. <PERSON> with a very long name that should wrap properly without overlapping borders',
      value: 'sarah_johnson',
      description: '<EMAIL>',
      subtitle: 'Cardiology • Doctor • Interventional Cardiology',
      status: 'active'
    },
    {
      id: 2,
      name: 'Nurse <PERSON>',
      value: 'emily_davis',
      description: '<EMAIL>',
      subtitle: 'Emergency • Nurse',
      status: 'active'
    },
    {
      id: 3,
      name: 'Dr. <PERSON>',
      value: 'michael_chen',
      description: '<EMAIL>',
      subtitle: 'Neurology • Doctor • Neurosurgery',
      status: 'on-leave'
    },
    {
      id: 4,
      name: 'Nurse <PERSON>',
      value: 'robert_wilson',
      description: '<EMAIL>',
      subtitle: 'ICU • Nurse',
      status: 'active'
    },
    {
      id: 5,
      name: 'Dr. Lisa Anderson',
      value: 'lisa_anderson',
      description: '<EMAIL>',
      subtitle: 'Pediatrics • Doctor • Pediatric Surgery',
      status: 'active'
    },
    {
      id: 6,
      name: 'Technician James Brown',
      label: 'Technician James Brown',
      value: 'james_brown',
      description: '<EMAIL>',
      subtitle: 'Radiology • Technician',
      status: 'inactive'
    },
    {
      id: 7,
      name: 'Test Long Content',
      label: 'This is a very long label that should wrap properly without overlapping the border on the right side of the container',
      value: 'test_long_content',
      description: '<EMAIL>',
      subtitle: 'Testing • Long Content • Word Wrapping',
      status: 'active'
    }
  ];

  const departments = [
    {
      id: 'cardiology',
      name: 'Cardiology Department',
      value: 'cardiology',
      description: 'Heart and cardiovascular care',
      status: 'active'
    },
    {
      id: 'emergency',
      name: 'Emergency Department',
      value: 'emergency',
      description: 'Emergency and urgent care services',
      status: 'active'
    },
    {
      id: 'neurology',
      name: 'Neurology Department',
      value: 'neurology',
      description: 'Brain and nervous system care',
      status: 'active'
    },
    {
      id: 'icu',
      name: 'Intensive Care Unit',
      value: 'icu',
      description: 'Critical care and monitoring',
      status: 'active'
    },
    {
      id: 'pediatrics',
      name: 'Pediatrics Department',
      value: 'pediatrics',
      description: 'Children and adolescent care',
      status: 'active'
    },
    {
      id: 'radiology',
      name: 'Radiology Department',
      value: 'radiology',
      description: 'Medical imaging and diagnostics',
      status: 'active'
    }
  ];

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', {
      selectedStaff: selectedStaff,
      selectedDepartments: selectedDepartments
    });
    alert('Check console for form data');
  };

  // Custom value handler for staff
  const handleCustomStaff = (name: string, description?: string) => ({
    id: `custom_${Date.now()}`,
    name: name,
    value: name.toLowerCase().replace(/\s+/g, '_'),
    description: description || `${name} (Custom Entry)`,
    subtitle: 'Custom • Staff Member',
    status: 'active',
    isCustom: true
  });

  // Custom value handler for departments
  const handleCustomDepartment = (name: string, description?: string) => ({
    id: `dept_${Date.now()}`,
    name: name,
    value: name.toLowerCase().replace(/\s+/g, '_'),
    description: description || `${name} department`,
    status: 'active',
    isCustom: true
  });

  return (
    <View className="min-h-screen bg-background py-8">
      <View className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <View className="text-center mb-8">
          <Text as="h1" className="text-4xl font-bold text-foreground mb-4">
            Hospital Management TransferList
          </Text>
          <Text as="p" className="text-lg text-muted-foreground max-w-3xl mx-auto">
            A modern, accessible dual-list selector designed for hospital management systems.
            Perfect for assigning staff, managing departments, and organizing resources.
          </Text>
        </View>

        {/* Staff Assignment Example */}
        <View className="bg-card rounded-xl shadow-sm border border-border p-6 mb-8">
          <Text as="h2" className="text-2xl font-semibold text-foreground mb-4">Staff Assignment</Text>
          <Text as="p" className="text-muted-foreground mb-6">
            Assign hospital staff to specific departments or projects with detailed information display.
          </Text>

          <TransferList
            label="Assign Staff Members"
            sourceData={hospitalStaff}
            selectedItems={selectedStaff}
            onSelectionChange={setSelectedStaff}
            sourceTitle="Available Staff"
            selectedTitle="Assigned Staff"
            height="200px"
            searchable={true}
            showCount={true}
            allowSelectAll={true}
            maxSelections={5}
            enableDragDrop={true}
            allowCustomValues={true}
            customValuePlaceholder="Add Custom Staff Member"
            onCustomValueAdd={handleCustomStaff}
            placeholder="Search staff by name, email, or department..."
            className="mb-4"
          />

          <View className="mt-4 p-4 bg-muted rounded-lg">
            <Text as="h4" className="font-medium text-foreground mb-2">Selected Staff:</Text>
            <Text as="p" className="text-sm text-muted-foreground">
              {selectedStaff.length > 0
                ? selectedStaff.map(staff => staff.name).join(', ')
                : 'No staff members selected'
              }
            </Text>
          </View>
        </View>

        {/* Department Management Example */}
        <View className="bg-card rounded-xl shadow-sm border border-border p-6 mb-8">
          <Text as="h2" className="text-2xl font-semibold text-foreground mb-4">Department Management</Text>
          <Text as="p" className="text-muted-foreground mb-6">
            Manage hospital departments with form integration and validation.
          </Text>

          <form onSubmit={handleFormSubmit} className="space-y-6">
            <TransferList
              label="Select Departments"
              sourceData={departments}
              selectedItems={selectedDepartments}
              onSelectionChange={setSelectedDepartments}
              sourceTitle="Available Departments"
              selectedTitle="Active Departments"
              height="200px"
              searchable={true}
              showCount={true}
              allowSelectAll={true}
              enableDragDrop={true}
              allowCustomValues={true}
              customValuePlaceholder="Add Custom Department"
              onCustomValueAdd={handleCustomDepartment}
              placeholder="Search departments..."
              required={true}
            />

            <button
              type="submit"
              className="w-full bg-primary hover:bg-primary-600 text-primary-foreground font-medium py-3 px-4 rounded-lg transition-colors duration-200"
            >
              Update Department Configuration
            </button>
          </form>
        </View>

        {/* Usage Information */}
        <View className="bg-card rounded-xl shadow-sm border border-border p-6">
          <Text as="h2" className="text-2xl font-semibold text-foreground mb-4">Usage Information</Text>
          <View className="space-y-4 text-sm text-muted-foreground">
            <Text as="p">• <strong>Click</strong> items to select/deselect them</Text>
            <Text as="p">• <strong>Drag and drop</strong> items between lists for quick movement</Text>
            <Text as="p">• <strong>Add custom values</strong> using the "Add Custom" button in selected list</Text>
            <Text as="p">• <strong>Search functionality</strong> works across all item properties including custom fields</Text>
            <Text as="p">• <strong>Reset button</strong> (🔄) clears all selections instantly</Text>
            <Text as="p">• <strong>Keyboard navigation</strong> supported with arrow keys and Enter/Space</Text>
            <Text as="p">• <strong>Value property</strong> included for form submission alongside name and description</Text>
            <Text as="p">• <strong>Fully accessible</strong> with screen reader support and ARIA labels</Text>
            <Text as="p">• <strong>Responsive design</strong>: side-by-side on desktop, stacked on mobile</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default TransferListExample;
