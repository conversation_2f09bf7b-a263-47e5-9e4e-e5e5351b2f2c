import Button from "@/components/button";
import Modal from "@/components/Modal";
import DataSort, { SortOption } from "@/components/SortData";
import Text from "@/components/text";
import { Card } from "@/components/ui/card";
import DynamicTable from "@/components/ui/DynamicTable";
import SearchBar from "@/components/ui/search-bar";
import View from "@/components/view";
import {
  MEDICINE_FORM_URL,
  MEDICINE_TABLE_URL,
  MEDICINE_DETAILS_URL,
} from "@/utils/urls/frontend";
import { Plus } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import PaginationComponent from "@/components/Pagination";
import ActionMenu from "@/components/editDeleteAction";
import { Link } from "react-router-dom";
import { handleSortChange } from "@/utils/helperFunctions";
import { useMedicine } from "@/actions/calls/medicine";
import dayjs from "dayjs";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import { GenericStatus } from "@/interfaces";

export const MedicinesPage = () => {
  const navigate = useNavigate();

  const [searchParams, setSearchParams] = useSearchParams();
  const { medicineListHandler, deleteMedicineHandler, cleanUp } = useMedicine();

  const currentSymbol = useSelector(
    (state: RootState) => state.systemSettings.settings.currency_symbol
  );

  const [deleteId, setDeleteId] = useState<null | string>(null);

  useEffect(() => {
    if (searchParams?.has("currentPage")) {
      medicineListHandler(
        searchParams?.get("currentPage") ?? 1,
        () => {},
        searchParams.get("search") ?? null,
        searchParams.get("sort_by") ?? null,
        searchParams.get("sort_order") ?? null
      );
    }
    return () => {
      cleanUp();
    };
  }, [
    searchParams?.get("currentPage"),
    searchParams.get("search"),
    searchParams.get("sort_by"),
    searchParams.get("sort_order"),
  ]);

  const modalCloseHandler = () => {
    setDeleteId(null);
  };

  const sortOptions: SortOption[] = [
    { label: "Medicine Name (A-Z)", value: "medicine_name", order: "asc" },
    { label: "Medicine Name (Z-A)", value: "medicine_name", order: "desc" },
    {
      label: "Generic Name (A-Z)",
      value: "generic_name",
      order: "asc",
    },
    {
      label: "Generic Name (Z-A)",
      value: "generic_name",
      order: "desc",
    },

    {
      label: "Dosage (A-Z)",
      value: "dosage_form",
      order: "asc",
    },
    {
      label: "Dosage (Z-A)",
      value: "dosage_form",
      order: "desc",
    },
    {
      label: "Stock (A-Z)",
      value: "stock_quantity",
      order: "asc",
    },
    {
      label: "Stock (Z-A)",
      value: "stock_quantity",
      order: "desc",
    },
    { label: "Unit Price (A-Z)", value: "unit_price", order: "asc" },
    { label: "Unit Price (Z-A)", value: "unit_price", order: "desc" },
    { label: "Expiry Date (A-Z)", value: "expiry_date", order: "asc" },
    { label: "Expiry Date (Z-A)", value: "expiry_date", order: "desc" },
    { label: "Status (A-Z)", value: "patient_number", order: "asc" },
    { label: "Status (Z-A)", value: "patient_number", order: "desc" },
  ];

  const [activeSort, setActiveSort] = useState<SortOption | null>(null);
  // const paginateObj = useSelector(
  //   (state: RootState) => state.examinations.userCompleteObj
  // );
  const paginateObj = useSelector(
    (state: RootState) => state.medicines.medicineListData
  );

  return (
    <React.Fragment>
      <Modal
        title="Examination Delete"
        isOpen={deleteId ? true : false}
        onClose={modalCloseHandler}
        description="Are you sure you want to delete this Examination? This action cannot be undone and will permanently remove the data from the system."
      >
        <View className="flex justify-end gap-2">
          <Button variant="outline" onPress={modalCloseHandler}>
            Cancel
          </Button>
          <Button
            variant="danger"
            onPress={() => {
              if (deleteId) {
                deleteMedicineHandler(deleteId, (_: boolean) => {
                  // if (success) {
                  medicineListHandler(
                    searchParams?.get("currentPage") ?? 1,
                    () => {
                      modalCloseHandler();
                    }
                  );
                  // }
                });
              }
            }}
          >
            Delete
          </Button>
        </View>
      </Modal>

      <View className="mb-6">
        <Text
          as="h1"
          weight="font-semibold"
          className="text-2xl font-bold text-text-DEFAULT mb-1"
        >
          Medicines
        </Text>
        <Text as="p" className="text-text-light">
          Manage hospital Medicines
        </Text>
      </View>

      <Card className="overflow-hidden">
        {/* <View className="p-4 border-b border-neutral-200 bg-card flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center  dark:border-none">
          <View className="flex gap-2 w-full  justify-between items-center ">
            <SearchBar
              onSearch={(value: string) => {
                setSearchParams({
                  ...Object.fromEntries([...searchParams]),
                  currentPage: "1",
                  search: value,
                });
              }}
              className="shadow-sm dark:shadow-none"
            />
            <View className="flex gap-3">
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(MEDICINE_TABLE_URL + MEDICINE_FORM_URL);
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Medicine
              </Button>
            </View>
          </View>
        </View> */}
        {/* Table */}
        <DynamicTable
          tableHeaders={[
            "Medicine Name",
            "Generic Name",
            "Dosage",
            "Stock",
            "Unit Price",
            "Expiry Date",
            "Status",
            "Actions",
          ]}
          tableData={paginateObj?.data?.map((medicine: any) => [
            <Link
              to={`${MEDICINE_TABLE_URL}${MEDICINE_DETAILS_URL}/${medicine.id}`}
              className="font-medium text-text-DEFAULT hover:text-secondary hover:underline"
            >
              {medicine?.medicine_name || "N/A"}
            </Link>,
            medicine?.generic_name || "N/A",
            medicine?.dosage_form || "N/A",
            medicine?.stock_quantity || "N/A",
            currentSymbol + medicine?.unit_price || "N/A",
            dayjs(medicine?.expiry_date).format("DD-MM-YYYY") || "N/A",
            <Text
              as="span"
              className={`inline-flex px-2 py-1 text-xs font-medium rounded-full`}
              style={getStatusColorScheme(
                medicine?.is_active
                  ? GenericStatus.ACTIVE
                  : GenericStatus.INACTIVE
              )}
            >
              {medicine?.is_active
                ? GenericStatus.ACTIVE
                : GenericStatus.INACTIVE}
            </Text>,

            <ActionMenu
              onEdit={() =>
                navigate(
                  MEDICINE_TABLE_URL + MEDICINE_FORM_URL + "/" + medicine.id
                )
              }
              onDelete={() => {
                setDeleteId(medicine.id);
              }}
            />,
          ])}
          header={{
            search: (
              <SearchBar
                onSearch={(value: string) => {
                  setSearchParams({
                    ...Object.fromEntries([...searchParams]),
                    currentPage: "1",
                    search: value,
                  });
                }}
                className="shadow-sm dark:shadow-none"
              />
            ),
            sort: (
              <DataSort
                sortOptions={sortOptions}
                onSort={(option) =>
                  handleSortChange(
                    option,
                    setActiveSort,
                    setSearchParams,
                    searchParams
                  )
                }
                activeSort={activeSort ?? undefined}
              />
            ),
            action: (
              <Button
                variant="primary"
                size="small"
                onPress={() => {
                  navigate(MEDICINE_TABLE_URL + MEDICINE_FORM_URL);
                }}
                className="flex items-center gap-2"
              >
                <Plus size={16} />
                Add Medicine
              </Button>
            ),
          }}
          footer={{
            pagination: (
              <PaginationComponent
                current_page={paginateObj?.current_page}
                last_page={paginateObj?.last_page}
                getPageNumberHandler={(page) =>
                  setSearchParams(
                    {
                      ...Object.fromEntries(searchParams),
                      currentPage: `${page}`,
                    },
                    { replace: true }
                  )
                }
              />
            ),
          }}
        />
        {/* <PaginationComponent
          getPageNumberHandler={(page) => {
            setSearchParams({
              ...Object.fromEntries([...searchParams]),
              currentPage: `${page}`,
            });
          }}
          last_page={paginateObj?.last_page}
          current_page={paginateObj?.current_page}
        /> */}
      </Card>
    </React.Fragment>
  );
};
