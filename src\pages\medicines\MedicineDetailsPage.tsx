import { useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import Button from "@/components/button";
import { Calendar, Pill, PillBottle, Building } from "lucide-react";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import dayjs from "dayjs";
import { useMedicine } from "@/actions/calls/medicine";
import InfoCard from "@/components/ui/infoCard";
import { Separator } from "@/components/ui/separator";
import Text from "@/components/text";
import View from "@/components/view";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import { GenericStatus } from "@/interfaces";

interface Medicine {
  medicine_name: string;
  generic_name?: string;
  dosage: string;
  dosage_unit?: string;
  strength?: string;
  strength_value?: number;
  strength_unit?: string;
  manufacturer: string;
  stock_quantity: number;
  unit_price: number;
  expiry_date?: Date;
  is_active: boolean;
}

const MedicineDetailsPage = () => {
  const params = useParams();
  const navigate = useNavigate();

  const { medicineDetailHandler } = useMedicine();

  const currentSymbol = useSelector(
    (state: RootState) => state.systemSettings.settings.currency_symbol
  );
  const medicineDetails = useSelector(
    (state: RootState) => state.medicines.medicineDetailData
  );

  useEffect(() => {
    if (params.id) {
      medicineDetailHandler(params.id, () => {});
    }
  }, [params.id]);

  // Helper to determine status badge color

  const medicine: Medicine = {
    medicine_name: "Acetaminophen",
    generic_name: "Paracetamol",
    dosage: "500",
    dosage_unit: "mg",
    strength: "500mg/tablet",
    strength_value: 500,
    strength_unit: "mg",
    manufacturer: "Acme Pharmaceuticals",
    stock_quantity: 250,
    unit_price: 0.15,
    expiry_date: new Date("2025-12-31"),
    is_active: true,
  };

  // Format currency
  // const formatCurrency = (amount: number) => {
  //   return new Intl.NumberFormat("en-US", {
  //     style: "currency",
  //     currency: "USD",
  //   }).format(amount);
  // };

  // Format date
  // const formatDate = (date?: Date) => {
  //   if (!date) return "N/A";
  //   return new Date(date).toLocaleDateString("en-US", {
  //     year: "numeric",
  //     month: "long",
  //     day: "numeric",
  //   });
  // };
  return (
    <View className="space-y-6 container mx-auto py-8">
      <View className="flex justify-between items-center">
        <View>
          <View className="flex items-center mb-2">
            <Text as="h1" weight="font-semibold" className="text-2xl font-bold">
              Medicine Details
            </Text>
            {/* <Badge 
              className={`ml-4 ${medicineDetails.is_active ? "bg-green-500 hover:bg-green-600" : "bg-red-500 hover:bg-red-600"}`}
            >
              {medicineDetails.is_active ? "Active" : "Inactive"}
            </Badge> */}
          </View>
          <Text as="p" className="text-muted-foreground">
            Detailed information about this medication
          </Text>
        </View>
        <Button
          variant="primary"
          className="mr-4 flex items-center justify-center gap-2"
          onPress={() => navigate(-1)}
        >
          Back to Home
        </Button>
      </View>

      {/* Basic Information Card */}
      <Card>
        <CardHeader
          className="flex"
          style={{
            justifyContent: "space-between",
            alignItems: "center",
            flexDirection: "row",
          }}
        >
          <CardTitle>Basic Information</CardTitle>
          <Text
            as="p"
            className={`ml-4 px-2 py-1 rounded-full`}
            style={getStatusColorScheme(
              medicineDetails?.is_active
                ? GenericStatus.ACTIVE
                : GenericStatus.INACTIVE
            )}
          >
            {medicineDetails?.is_active ? "Active" : "Inactive"}
          </Text>
        </CardHeader>
        <CardContent>
          <View className="grid grid-cols-1 gap-6">
            <View className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <View>
                <Text as="h3" className="text-xl font-semibold">
                  {medicineDetails?.medicine_name || "N/A"}
                </Text>
                {medicineDetails?.generic_name && (
                  <Text as="p" className="text-gray-500">
                    {medicineDetails?.generic_name || "N/A"}
                  </Text>
                )}
              </View>

              <View className="mt-2 sm:mt-0 text-right">
                <Text as="p" className="!text-2xl font-bold text-primary">
                  {currentSymbol + medicineDetails?.unit_price || "N/A"}
                </Text>
                <Text className="text-gray-500 text-sm">per unit</Text>
              </View>
            </View>

            <Separator className="my-2" />

            <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <InfoCard
                label="Dosage"
                value={`${medicineDetails?.dosage_form || "N/A"} `}
                icon={<Pill className="h-5 w-5 text-primary" />}
              />

              {medicine.strength && (
                <InfoCard
                  label="Strength"
                  value={
                    medicineDetails?.strength +
                      medicineDetails?.strength_unit || "N/A"
                  }
                  icon={<PillBottle className="h-5 w-5 text-primary" />}
                />
              )}

              <InfoCard
                label="Manufacturer"
                value={medicineDetails?.manufacturer || "N/A"}
                icon={<Building className="h-5 w-5 text-primary" />}
              />

              {medicine.expiry_date && (
                <InfoCard
                  label="Expiry Date"
                  value={
                    dayjs(medicineDetails?.expiry_date).format("YYYY-MM-DD") ||
                    "N/A"
                  }
                  icon={<Calendar className="h-5 w-5 text-primary" />}
                />
              )}
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Inventory Status Card */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Status</CardTitle>
        </CardHeader>
        <CardContent>
          <View className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="border-none shadow-none">
              <CardContent className="p-0">
                <Text as="h4" className="text-sm text-muted-foreground mb-1">
                  Current Stock
                </Text>
                <Text as="p" className="!text-3xl font-bold">
                  {medicineDetails?.stock_quantity}
                  <Text
                    as="span"
                    className="text-base font-normal text-muted-foreground ml-2"
                  >
                    units
                  </Text>
                </Text>
              </CardContent>
            </Card>

            <Card className="border-none shadow-none">
              <CardContent className="p-0">
                <Text as="h4" className="text-sm text-muted-foreground mb-1">
                  Total Value
                </Text>
                <Text className="!text-3xl font-bold">
                  {currentSymbol +
                    medicineDetails?.stock_quantity *
                      medicineDetails?.unit_price}
                </Text>
              </CardContent>
            </Card>

            <Card className="border-none shadow-none">
              <CardContent className="p-0">
                <Text as="h4" className="text-sm text-muted-foreground mb-1">
                  Stock Status
                </Text>
                <View
                  className={`rounded-md p-4 ${
                    medicineDetails?.stock_quantity > 100
                      ? "bg-green-50 border border-success text-success"
                      : medicineDetails?.stock_quantity > 20
                      ? "bg-yellow-50 border border-yellow-200 text-warning"
                      : "bg-red-100 border border-danger"
                  }`}
                >
                  <Text
                    as="p"
                    className={`font-medium ${
                      medicineDetails?.stock_quantity > 100
                        ? "text-green-700"
                        : medicineDetails?.stock_quantity > 20
                        ? "text-yellow-700"
                        : "text-danger"
                    }`}
                  >
                    {medicineDetails?.stock_quantity > 100
                      ? "Well stocked"
                      : medicineDetails?.stock_quantity > 20
                      ? "Stock running low"
                      : "Critical stock level"}
                  </Text>
                </View>
              </CardContent>
            </Card>
          </View>
        </CardContent>
      </Card>
    </View>
  );
};

export default MedicineDetailsPage;
