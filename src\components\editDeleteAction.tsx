import Button from "./button";
import { Download, Edit, Eye, Trash } from "lucide-react";
import View from "./view";
interface ActionMenuProps {
  onEdit?: () => void;
  onDelete?: () => void;
  onView?: () => void;
  onDownload?: () => void;
  editTitle?: string;
  deleteTitle?: string;
  downloadTitle?: string;
}
const ActionMenu: React.FC<ActionMenuProps> = ({
  onEdit,
  onDelete,
  onView,
  onDownload,
  editTitle,
  deleteTitle,
  downloadTitle,
}: any) => {
  const handleEdit = () => {
    if (onEdit) onEdit();
  };

  const handleDelete = () => {
    if (onDelete) onDelete();
  };
  const handleDownload = () => {
    if (onDownload) onDownload();
  };
  const handleView = () => {
    if (onView) onView();
  };

  return (
    <>
      <View className="relative flex items-center">
        {onView && (
          <Button
            variant="ghost"
            onPress={handleView}
            className="flex items-center w-auto px-2 py-2 text-sm text-green-600 hover:bg-primary-100"
            title={downloadTitle || ""}
          >
            <Eye size={18} className=" text-primary" />
          </Button>
        )}
        {onEdit && (
          <Button
            variant="ghost"
            onPress={handleEdit}
            className="flex items-center w-auto px-2 py-2 text-sm text-primary-700 hover:bg-primary-100"
            title={editTitle || ""}
          >
            <Edit size={18} className=" text-primary" />
          </Button>
        )}
        {onDelete && (
          <Button
            variant="ghost"
            onPress={handleDelete}
            className="flex items-center w-auto px-2 py-2 text-sm text-red-600 hover:bg-red-100"
            title={deleteTitle || ""}
          >
            <Trash size={18} className=" text-danger" />
          </Button>
        )}
        {onDownload && (
          <Button
            variant="ghost"
            onPress={handleDownload}
            className="flex items-center w-auto px-2 py-2 text-sm text-green-600 hover:bg-primary-100"
            title={downloadTitle || ""}
          >
            <Download size={18} className=" text-primary" />
          </Button>
        )}
      </View>
    </>
  );
};

export default ActionMenu;
