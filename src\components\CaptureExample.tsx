import React, { useState } from 'react';
import WebcamCapture from './Capture';
import { ExtendedFileItem } from '@/interfaces/components/input/uploadProps';
import View from './view';
import Text from './text';

const CaptureExample: React.FC = () => {
  const [files, setFiles] = useState<ExtendedFileItem[]>([]);

  const handleFileChange = (fileList: ExtendedFileItem[]) => {
    setFiles(fileList);
    console.log('📁 Files updated:', fileList);

    // Check if a new photo was captured
    const newCapturedFiles = fileList.filter(file =>
      file.name.includes('webcam-capture') && file.file
    );

    if (newCapturedFiles.length > 0) {
      console.log('📸 New photo captured! Camera should be off now.');
    }
  };

  const handleUpload = async (uploadFiles: File[]) => {
    console.log('Uploading files:', uploadFiles);
    // Simulate upload process
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        console.log('Upload completed');
        resolve();
      }, 1000);
    });
  };

  return (
    <View className="max-w-4xl mx-auto p-6 space-y-8">
      <View className="text-center">
        <Text as="h1" className="text-3xl font-bold text-gray-900 mb-2">
          Enhanced Upload with Camera Capture
        </Text>
        <Text as="p" className="text-gray-600">
          Upload files or capture photos using your webcam. All captured photos appear in the file list.
        </Text>
      </View>

      {/* Enhanced Upload Component */}
      <View className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
        <WebcamCapture
          name="documents"
          label="Upload Documents or Capture Photos"
          accept="image/*,.pdf,.doc,.docx"
          multiple={true}
          maxSize={5 * 1024 * 1024} // 5MB
          maxCount={10}
          required={false}
          placeholder="Capture Photo"
          browseText="Upload Files"
          helperText="You can upload files or capture photos using your webcam. Max 5MB per file, up to 10 files."
          showFileList={true}
          showPreview={true}
          onChange={handleFileChange}
          onUpload={handleUpload}
          quality={0.9}
          width={1280}
          height={720}
        />
      </View>

      {/* File Summary */}
      <View className="bg-gray-50 rounded-xl p-6">
        <Text as="h3" className="text-lg font-semibold text-gray-900 mb-4">
          File Summary
        </Text>
        {files.length === 0 ? (
          <Text as="p" className="text-gray-500">
            No files selected yet. Upload files or capture photos to see them here.
          </Text>
        ) : (
          <View className="space-y-2">
            <Text as="p" className="text-sm text-gray-600">
              Total files: {files.length}
            </Text>
            <View className="grid gap-2">
              {files.map((file, index) => (
                <View key={file.id} className="flex items-center gap-3 p-3 bg-white rounded-lg border">
                  <View className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Text as="span" className="text-xs font-medium text-blue-600">
                      {index + 1}
                    </Text>
                  </View>
                  <View className="flex-1">
                    <Text as="p" className="text-sm font-medium text-gray-900">
                      {file.name}
                    </Text>
                    <Text as="p" className="text-xs text-gray-500">
                      {(file.size / 1024).toFixed(1)} KB • {file.type}
                      {file.isExisting && (
                        <span className="ml-2 text-blue-500">Existing file</span>
                      )}
                    </Text>
                  </View>
                  <View className="text-right">
                    <Text as="span" className={`text-xs px-2 py-1 rounded-full ${
                      file.file ? 'bg-green-100 text-green-700' : 'bg-blue-100 text-blue-700'
                    }`}>
                      {file.file ? 'New' : 'Existing'}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}
      </View>

      {/* Usage Instructions */}
      <View className="bg-blue-50 rounded-xl p-6">
        <Text as="h3" className="text-lg font-semibold text-blue-900 mb-4">
          How to Use
        </Text>
        <View className="space-y-3 text-sm text-blue-800">
          <View className="flex items-start gap-3">
            <Text as="span" className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold">1</Text>
            <Text as="p"><strong>Upload Files:</strong> Click "Upload Files" to select files from your device</Text>
          </View>
          <View className="flex items-start gap-3">
            <Text as="span" className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold">2</Text>
            <Text as="p"><strong>Capture Photos:</strong> Click "Capture Photo" to open the camera and take pictures</Text>
          </View>
          <View className="flex items-start gap-3">
            <Text as="span" className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold">3</Text>
            <Text as="p"><strong>Multiple Captures:</strong> You can capture multiple photos - each one will be added to the file list</Text>
          </View>
          <View className="flex items-start gap-3">
            <Text as="span" className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center text-xs font-bold">4</Text>
            <Text as="p"><strong>File Management:</strong> Remove individual files or clear all files using the controls in the file list</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default CaptureExample;
