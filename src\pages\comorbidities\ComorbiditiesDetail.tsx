import { useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  Activity,
  Calendar,
  AlertCircle,
  CheckCircle,
  Clock,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Button from "@/components/button";

interface ComorbidityData {
  name: string;
  description: string;
  is_chronic: number;
  is_active: string;
}

// Mock data - replace with actual data fetching
const mockComorbidity: ComorbidityData = {
  name: "BP",
  description: "bp",
  is_chronic: 1,
  is_active: "Active",
};

const ComorbidityDetail = () => {
  const navigate = useNavigate();

  // In a real app, you'd fetch data based on the ID
  const comorbidity = mockComorbidity;

  const getStatusBadge = (status: string) => {
    const isActive = status.toLowerCase() === "active";
    return (
      <Badge
        variant={isActive ? "default" : "secondary"}
        className="font-medium"
      >
        {isActive ? (
          <CheckCircle className="w-3 h-3 mr-1" />
        ) : (
          <Clock className="w-3 h-3 mr-1" />
        )}
        {status}
      </Badge>
    );
  };

  const getChronicBadge = (isChronic: number) => {
    const chronic = isChronic === 1;
    return (
      <Badge
        variant={chronic ? "destructive" : "outline"}
        className="font-medium"
      >
        {chronic ? (
          <AlertCircle className="w-3 h-3 mr-1" />
        ) : (
          <Activity className="w-3 h-3 mr-1" />
        )}
        {chronic ? "Chronic" : "Acute"}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-muted/30 to-muted/50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex justify-end mb-4">
            <Button
              className="flex items-center gap-2"
              variant="outline"
              size="small"
              onClick={() => navigate(-1)}
            >
              Back to Comorbidity
            </Button>
          </div>

          <div className="bg-card/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border">
            <div className="flex items-start justify-between">
              <div>
                <h1 className="text-3xl font-bold text-foreground mb-2">
                  {comorbidity.name}
                </h1>
                <p className="text-muted-foreground text-lg">
                  Comorbidity Details
                </p>
              </div>
              <div className="flex gap-2">
                {getStatusBadge(comorbidity.is_active)}
                {getChronicBadge(comorbidity.is_chronic)}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information Card */}
          <Card className="bg-card/80 backdrop-blur-sm shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-foreground">
                <Activity className="w-5 h-5 mr-2 text-primary" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-primary/10 rounded-lg p-4 border border-primary/20">
                <label className="text-sm font-medium text-primary mb-1 block">
                  Condition Name
                </label>
                <p className="text-lg font-semibold text-foreground">
                  {comorbidity.name}
                </p>
              </div>

              <div className="bg-muted rounded-lg p-4">
                <label className="text-sm font-medium text-muted-foreground mb-1 block">
                  Description
                </label>
                <p className="text-foreground leading-relaxed">
                  {comorbidity.description || "No description available"}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Status Information Card */}
          <Card className="bg-card/80 backdrop-blur-sm shadow-lg">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-foreground">
                <Calendar className="w-5 h-5 mr-2 text-secondary-foreground" />
                Status Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-accent/50 rounded-lg p-4 border border-accent">
                <label className="text-sm font-medium text-accent-foreground mb-2 block">
                  Current Status
                </label>
                <div className="flex items-center">
                  {getStatusBadge(comorbidity.is_active)}
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {comorbidity.is_active.toLowerCase() === "active"
                    ? "This condition is currently being monitored and managed"
                    : "This condition is not currently active"}
                </p>
              </div>

              <div className="bg-secondary/50 rounded-lg p-4 border border-secondary">
                <label className="text-sm font-medium text-secondary-foreground mb-2 block">
                  Condition Type
                </label>
                <div className="flex items-center">
                  {getChronicBadge(comorbidity.is_chronic)}
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  {comorbidity.is_chronic === 1
                    ? "Long-term condition requiring ongoing management"
                    : "Short-term or acute condition"}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Summary Card */}
        <Card className="mt-6 bg-card/80 backdrop-blur-sm shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center text-foreground">
              <AlertCircle className="w-5 h-5 mr-2 text-primary" />
              Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gradient-to-r from-muted/50 to-accent/30 rounded-lg p-6 border">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="p-4">
                  <div className="text-2xl font-bold text-primary mb-1">
                    {comorbidity.name}
                  </div>
                  <div className="text-sm text-muted-foreground">Condition</div>
                </div>

                <div className="p-4">
                  <div className="text-2xl font-bold text-secondary-foreground mb-1">
                    {comorbidity.is_active}
                  </div>
                  <div className="text-sm text-muted-foreground">Status</div>
                </div>

                <div className="p-4">
                  <div className="text-2xl font-bold text-accent-foreground mb-1">
                    {comorbidity.is_chronic === 1 ? "Chronic" : "Acute"}
                  </div>
                  <div className="text-sm text-muted-foreground">Type</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ComorbidityDetail;
