import View from "@/components/view";
import Input from "@/components/input";
import React, { useState } from "react";
import Button from "@/components/button";
import Select from "@/components/Select";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { useParams } from "react-router-dom";
import { validationForm } from "./validationForm";
import { useInvoice } from "@/actions/calls/invoice";
import { toast } from "@/utils/custom-hooks/use-toast";
import { amountOptions } from "../forms/appointmentsForm/appointmentFormOptions";

const PaymentSection: React.FC<{}> = () => {
  const { id } = useParams();
  const [amount, setAmount] = useState<string>("");
  const [amountFor, setAmountFor] = useState<string>("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const { invoicePayment, getPaymentDetailHandler } = useInvoice();
  const invoiceData = useSelector(
    (state: RootState) => state.invoice.invoiceDetailData
  );

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const data: any = {};
    try {
      for (let [key, value] of formData.entries()) {
        data[key as keyof any] = value as any;
      }
      await validationForm.validate(data, { abortEarly: false });
      setErrors({});
      data["patient_id"] = invoiceData?.patient_id;
      data["appointment_id"] = invoiceData?.appointment_id;
      data["consultation_id"] = invoiceData?.id;
      data["front_desk_user_id"] = invoiceData?.front_desk_user_id;
      data["doctor_id"] = invoiceData?.doctor_id;
      invoicePayment(data, (success: boolean) => {
        if (success && id) {
          setAmount("");
          setAmountFor("");
          getPaymentDetailHandler(id, () => {});
          toast({
            title: "Success!",
            description: "Successfully payment added",
            variant: "success",
          });
        } else {
          toast({
            title: "Error",
            description: "Failed to payment added",
            variant: "destructive",
          });
        }
      });
    } catch (e) {
      console.error(e);
    }
  };
  return (
    <React.Fragment>
      <form onSubmit={handleSubmit}>
        <View className="space-y-4 my-4">
          <View>
            <Input
              required={true}
              id="amount"
              name="amount"
              value={amount}
              error={errors?.amount}
              label="Amount"
              placeholder="Enter Amount"
              onChange={(e) => {
                setAmount(e.target.value);
              }}
            />
          </View>
          <View>
            <Select
              id="amount_for"
              required={true}
              name="amount_for"
              value={amountFor}
              label="Amount For"
              options={amountOptions}
              error={errors?.amount_for}
              placeholder="Select Amount For"
              onChange={(e) => {
                setAmountFor(e.target.value);
              }}
            />
          </View>
          <View className="flex justify-end my-4">
            <Button type="submit">Submit</Button>
          </View>
        </View>
      </form>
    </React.Fragment>
  );
};

export default PaymentSection;
