import { combineReducers, configureStore } from "@reduxjs/toolkit";
import roomReducer from "./slices/room";
import systemSettingsReducer from "./slices/systemSettingsSlice";
import userReducer from "./slices/userSlice";
import authentication from "@/actions/slices/auth";
import patient from "@/actions/slices/patient";
import geography from "@/actions/slices/geography";
import dashboard from "@/actions/slices/dashboard";
import roleReducer from "@/actions/slices/roleSlice";
import opd from "@/actions/slices/opd";
import allergies from "@/actions/slices/allergies";
import consultation from "@/actions/slices/consultation";
import appointmentReducer from "@/actions/slices/appointments";
import examinationReducer from "@/actions/slices/examination";
import test from "@/actions/slices/test";
import patientTest from "@/actions/slices/patientTest";
import medicineReducer from "@/actions/slices/medicine";
import medicineCategory from "@/actions/slices/medicineCategory";
import medicineCategoryMapping from "@/actions/slices/medicineCategoryMapping";
import findingReducer from "@/actions/slices/findings";
import yogaAsana from "@/actions/slices/yogaAsana";
import doshaAnalysisReducer from "./slices/doshaAnalysis";
import invoice from "./slices/invoice";
import departmentReducer from "./slices/departments";
import consultationFeesReducer from "./slices/consultationFees";
import surgicalHistory from "./slices/surgicalHistory";
import chiefComplaintReducer from "./slices/chiefComplaints";
import amountTypeReducer from "./slices/amountType";


const rootReducer = combineReducers({
  authentication,
  patient,
  geography,
  dashboard,
  opd,
  allergies,
  // medicine,
  consultation,
  test,
  patientTest,
  medicineCategory,
  medicineCategoryMapping,
  yogaAsana,
  invoice,
  surgicalHistory,

  room: roomReducer,
  systemSettings: systemSettingsReducer,
  users: userReducer,
  roles: roleReducer,
  appointment: appointmentReducer,
  examinations: examinationReducer,
  medicines: medicineReducer,
  findings: findingReducer,
  doshaAnalysis: doshaAnalysisReducer,
  department: departmentReducer,
  consultationFees: consultationFeesReducer,
  chiefComplaint: chiefComplaintReducer,
  amountType: amountTypeReducer,

});

export const store = configureStore({
  reducer: rootReducer,
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RootState = ReturnType<typeof store.getState>;
// // Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;
