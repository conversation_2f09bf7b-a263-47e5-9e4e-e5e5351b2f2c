import { RootState } from "@/actions/store";
import Input from "@/components/input";
import Select from "@/components/Select";
import Textarea from "@/components/Textarea";
import View from "@/components/view";
import {
  Diagnosis,
  diagnosisStatusOptions,
} from "@/interfaces/slices/diagnosis";
import useForm from "@/utils/custom-hooks/use-form";
import { useSelector } from "react-redux";

interface SectionOneProps {
  errorsDiagnosisName: string;
  errorsStatus: string;
}
const SectionOne: React.FC<SectionOneProps> = ({
  errorsDiagnosisName,
  errorsStatus,
}) => {
  const diagnosisData = useSelector(
    (state: RootState) => state.diagnosis.diagnosisDetails
  );
  const { values, handleChange, onSetHandler } =
    useForm<Partial<Diagnosis> | null>(diagnosisData);
  return (
    <>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
        <View>
          <Input
            required={true}
            id="diagnosis_name"
            name="diagnosis_name"
            label="Diagnosis Name"
            onChange={handleChange}
            error={errorsDiagnosisName}
            placeholder="Diagnosis Name"
            value={values?.diagnosis_name}
          />
        </View>
        <View>
          <Input
            id="icd_code"
            name="icd_code"
            label="ICD Code"
            onChange={handleChange}
            placeholder="ICD Code"
            value={values?.icd_code}
          />
        </View>
      </View>
      <View>
        <Textarea
          id="description"
          label="Description"
          name="description"
          placeholder="Description"
          value={values?.description ?? ""}
          onChange={handleChange}
        />
      </View>
      <View>
        <Select
          id="is_active"
          required={true}
          name="is_active"
          label="Status"
          error={errorsStatus}
          placeholder="Status"
          value={values?.is_active + ""}
          options={diagnosisStatusOptions}
          onChange={(e) => onSetHandler("is_active", e.target.value)}
        />
      </View>
    </>
  );
};

export default SectionOne;
