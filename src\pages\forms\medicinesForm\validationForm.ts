import * as Yup from "yup";

export const validationForm = Yup.object({
medicine_name: Yup.string()
    .required("Medicine name is required"),

  generic_name: Yup.string(),

  manufacturer: Yup.string()
    .required("Manufacturer is required"),

  is_active: Yup.boolean()
    .required("Status is required"),

  dosage_form: Yup.string()
    .required("Dosage is required"),

  strength: Yup.string()
    .required("Strength is required"),

  stock_quantity: Yup.number()
    .typeError("Stock quantity must be a number")
    .min(0, "Cannot be negative")
    .required("Stock quantity is required"),

  unit_price: Yup.number()
    .typeError("Unit price must be a number")
    .min(0, "Cannot be negative")
    .required("Unit price is required"),

  expiry_date: Yup.date()
    .typeError("Invalid date format")
    .min(new Date(), "Expiry date must be in the future")
    .required("Expiry date is required"),
 
});
