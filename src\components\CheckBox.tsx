import React from 'react';

interface CheckboxProps {
//   label?: string;
  checked: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
  name?: string;
  id?: string;
  className?: string;
}

const Checkbox: React.FC<CheckboxProps> = ({
//   label,
  checked,
  onChange,
  disabled = false,
  name,
  id,
  className = '',
}) => {
  return (
    <label className={`flex items-center space-x-2 cursor-pointer ${disabled ? 'opacity-60 cursor-not-allowed' : ''}`}>
      <input
        type="checkbox"
        id={id}
        name={name}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        className={`form-checkbox h-5 w-5 text-primary ${className}`}
      />
      {/* {label && <span className="text-sm text-gray-800">{label}</span>} */}
    </label>
  );
};

export default Checkbox;
