import { RootState } from "@/actions/store";
import Input from "@/components/input";
import Select from "@/components/Select";
import Textarea from "@/components/Textarea";
import View from "@/components/view";
import {
  Comorbidity,
  comorbidityStatusTyeOptions,
  isChronicTypeOptions,
} from "@/interfaces/slices/comorbidities";
import useForm from "@/utils/custom-hooks/use-form";
import { useSelector } from "react-redux";

interface SectionOneProps {
  errorsName: string;
  errorsIsChronic: string;
  errorsStatus: string;
}
const SectionOne: React.FC<SectionOneProps> = ({
  errorsName,
  errorsIsChronic,
  errorsStatus,
}) => {
  const ComorbidityData = useSelector(
    (state: RootState) => state.comorbidities.comorbidityDetails
  );
  const { values, handleChange, onSetHandler } =
    useForm<Partial<Comorbidity> | null>(ComorbidityData);
  return (
    <>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
        <View>
          <Input
            id="name"
            name="name"
            required={true}
            error={errorsName}
            value={values?.name}
            label="Comorbidity Name"
            onChange={handleChange}
            placeholder="BP, Diabetes, etc."
          />
        </View>
        <View>
          <Select
            id="is_chronic"
            required={true}
            name="is_chronic"
            label="Is Chronic"
            error={errorsIsChronic}
            placeholder="Is Chronic"
            value={values?.is_chronic}
            options={isChronicTypeOptions}
            onChange={(e) => onSetHandler("is_chronic", e.target.value)}
          />
        </View>
      </View>
      <View>
        <Textarea
          id="description"
          label="Description"
          name="description"
          placeholder="Description"
          value={values?.description ?? ""}
          onChange={handleChange}
        />
      </View>
      <View>
        <Select
          id="is_active"
          required={true}
          name="is_active"
          label="Status"
          error={errorsStatus}
          placeholder="Status"
          value={values?.is_active + ""}
          options={comorbidityStatusTyeOptions}
          onChange={(e) => onSetHandler("is_active", e.target.value)}
        />
      </View>
    </>
  );
};

export default SectionOne;
