import { ServiceCostState } from "@/interfaces/master/serviceCost";
import { createSlice } from "@reduxjs/toolkit";

const initialState: ServiceCostState = {
  serviceCostDetailData: {},
  serviceCostListData: [],
  serviceCostDropdownData: [],
};

const serviceCostSlice = createSlice({
  name: "serviceCost",
  initialState,
  reducers: {
    serviceCostDetailSlice: (state, action) => {
      state.serviceCostDetailData = action?.payload;
    },
    serviceCostListSlice: (state, action) => {
      state.serviceCostListData = action?.payload;
    },
    serviceCostDropdownSlice: (state, action) => {
      state.serviceCostDropdownData = action?.payload;
    },
    clearserviceCostDetailSlice: (state) => {
      state.serviceCostDetailData = null;
    },
  },
});

export const {
  serviceCostDetailSlice,
  serviceCostListSlice,
  serviceCostDropdownSlice,
  clearserviceCostDetailSlice,
} = serviceCostSlice.actions;

export default serviceCostSlice.reducer;
