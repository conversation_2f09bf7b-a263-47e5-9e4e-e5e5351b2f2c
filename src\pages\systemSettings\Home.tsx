import { Save } from "lucide-react";
import View from "@/components/view";
import Text from "@/components/text";
import React, { useState } from "react";
import Button from "@/components/button";
import PrefixesSection from "./PrefixesSection";
import EmailNotification from "./EmailNotification";
import { useColors } from "@/contexts/ColorContext";
import ColorSchemeSection from "./ColorSchemeSection";
import { validationSchema } from "./validationSchema";
import { toast } from "@/utils/custom-hooks/use-toast";
import { imageUpload } from "@/actions/calls/uesImage";
import ThemeSettingSection from "./ThemeSettingSection";
import GeneralSettingsSection from "./GeneralSettingsSection";
import { useSystemSettings } from "@/actions/calls/systemSettings";
import { SystemSettings, Theme } from "@/interfaces/systemSettings";

const SystemSettingsPage = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const { colors } = useColors();
  const { getSystemSettings, editOrAddSystemSetting } = useSystemSettings();

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    try {
      // Validate form data using Yup
      const formData = new FormData(e.currentTarget);
      let logoFile: File | null = null;

      const systemSettingsObj: Partial<SystemSettings> = {};
      for (const [key, value] of formData.entries()) {
        // Cast to any to avoid TypeScript errors with complex types
        // (systemSettingsObj as any)[key] = value;
        if (formData.get("email_notification") === "on") {
          (systemSettingsObj as any)["email_notification"] = true;
        } else {
          (systemSettingsObj as any)["email_notification"] = false;
        }
        if (key === "hospital_logo" && value instanceof File) {
          if (value.size > 0) {
            logoFile = value;
          }
        } else {
          (systemSettingsObj as any)[key] = value;
        }
      }

      await validationSchema.validate(systemSettingsObj, { abortEarly: false });
      setIsSubmitting(true);
      setErrors({});

      // Add color values from the ColorContext
      Object.entries(colors).forEach(([key, value]) => {
        if (!(key in systemSettingsObj)) {
          // Cast to any to avoid TypeScript errors with complex types
          (systemSettingsObj as any)[key] = value;
        }
      });

      if ("hospital_logo" in systemSettingsObj) {
        delete systemSettingsObj.hospital_logo;
      }

      editOrAddSystemSetting(systemSettingsObj, (success, response) => {
        setIsSubmitting(false);
        if (success && response?.data?.id) {
          getSystemSettings();
          // updateColorVariables({
          //   primary_color: colors.primary_color,
          //   secondary_color: colors.secondary_color,
          //   tertiary_color: colors.tertiary_color
          // });
          localStorage.setItem(
            "theme",
            Object.values(Theme).includes(systemSettingsObj?.theme as Theme)
              ? (systemSettingsObj?.theme as Theme)
              : Theme.LIGHT
          );
          if (logoFile && typeof logoFile !== "string") {
            const imageUploaddata = {
              id: response?.data?.id,
              modal_type: "system_settings",
              file_name: "hospital_logo",
              folder_name: "hospital_image",
              image: logoFile,
            };
            imageUpload(imageUploaddata, (uploadSuccess, _) => {
              if (!uploadSuccess) {
                toast({
                  title: "Error!",
                  description: "Failed to upload image",
                  variant: "destructive",
                });
              }
            });
          }
          toast({
            title: "Settings saved",
            description: "Your system settings have been updated successfully.",
            variant: "success",
          });
        } else {
          toast({
            title: "Error!",
            description: "Failed to update settings",
            variant: "destructive",
          });
        }
      });
    } catch (err: any) {
      setIsSubmitting(false);
      // if (err instanceof yup.ValidationError) {
      //   console.error(err);
      //   const validationErrors: Record<string, string> = {};
      //   err.inner.forEach((error) => {
      //     if (error.path) {
      //       validationErrors[error.path] = error.message;
      //     }
      //   });
      // }
      // setIsSubmitting(false);
      if (err.inner) {
        const errors: Record<string, string> = {};
        err.inner.forEach((e: any) => {
          if (e.path) errors[e.path] = e.message;
        });
        setErrors(errors);

        toast({
          title: "Error!",
          description: "Something went wrong! Please check your input fields.",
          variant: "destructive",
        });
      }
    }
  };

  return (
    <React.Fragment>
      <View className="space-y-6">
        <View>
          <Text
            as="h1"
            weight="font-semibold"
            className="text-2xl font-bold text-text-DEFAULT"
          >
            System Settings
          </Text>
          <Text as="p" className="text-text-light">
            Customize your hospital management system
          </Text>
        </View>

        <form
          onSubmit={handleSubmit}
          className="space-y-8"
          encType="multipart/form-data"
        >
          {/* General Settings Section */}
          <GeneralSettingsSection
            errorsCurrency={errors.currency}
            errorsHospitalName={errors.hospital_name}
            errorsCurrencySymbol={errors.currency_symbol}
          />
          {/* Prefixes Section */}
          <PrefixesSection
            errorsIpdPrefix={errors.ipd_prefix}
            errorsOpdPrefix={errors.opd_prefix}
            errorsTestPrefix={errors.test_prefix}
            errorsPatientPrefix={errors.patient_prefix}
            errorsPaymentPrefix={errors.payment_prefix}
            errorsInvoicePrefix={errors.invoice_prefix}
            errorsHospitalPrefix={errors.hospital_prefix}
            errorsFindingsPrefix={errors.findings_prefix}
            errorsAppointmentPrefix={errors.appointment_prefix}
          />

          {/* Theme Settings */}
          <ThemeSettingSection errorsTheme={errors.theme} />
          <EmailNotification />

          {/* Color Scheme */}
          <ColorSchemeSection
            errorsPrimaryColor={errors.primary_color}
            errorsTertiaryColor={errors.tertiary_color}
            errorsSecondaryColor={errors.secondary_color}
            errorsBgPrimaryColor={errors.bg_primary_color}
            errorsBgTertiaryColor={errors.bg_tertiary_color}
            errorsBgSecondaryColor={errors.bg_secondary_color}
            errorsTextPrimaryColor={errors.text_primary_color}
            errorsTextTertiaryColor={errors.text_tertiary_color}
            errorsTextSecondaryColor={errors.text_secondary_color}
          />

          <View className="flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center gap-1.5"
              onPress={() => handleSubmit}
            >
              <Save className="h-4 w-4" />
              {isSubmitting ? "Saving..." : "Save Settings"}
            </Button>
          </View>
        </form>
      </View>
    </React.Fragment>
  );
};

export default SystemSettingsPage;
