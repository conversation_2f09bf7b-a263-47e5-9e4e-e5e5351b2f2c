import React from "react";
import NotFound from "@/pages/NotFound";
import Dashboard from "@/pages/dashboard/Home";
import UsersPage from "@/pages/users/UsersPage";
import { Navigate, Route, Routes } from "react-router-dom";
import Register from "@/pages/forms/userForm/user";
import PatientsPage from "@/pages/patient/PatientsPage";
import UserDetailPage from "@/pages/users/UserDetailPage";
import DashboardLayout from "@/components/DashboardLayout";
import PatientAdmissionForm from "@/pages/forms/patientForm/patient";
import PatientDetailsPage from "@/pages/patient/PatientsDetailsPage";
import {
  USER_URL,
  SETTINGS_URL,
  DASHBOARD_URL,
  EDIT_USER_URL,
  USER_TABLE_URL,
  FIRST_PAGE_URL,
  USER_DETAIL_URL,
  PATIENT_TABLE_URL,
  PATIENTS_FORM_URL,
  PATIENT_DETAIL_URL,
  OPD_FORM_URL,
  OPD_TABLE_URL,
  ROOMS_TABLE_URL,
  ROOMS_FORM_URL,
  APPOINTMENT_TABLE_URL,
  APPOINTMENT_FORM_URL,
  ROLES_TABLE_URL,
  ROLES_URL,
  ROOMS_DETAIL_URL,
  EDIT_ROLE_URL,
  OPD_DETAIL_URL,
  EDIT_OPD_URL,
  APPOINTMENT_DETAILS_URL,
  CONSULTATION_TABLE_URL,
  CONSULTATION_FORM_URL,
  EXAMINATION_TABLE_URL,
  EXAMINATION_FORM_URL,
  EXAMINATION_DETAILS_URL,
  CONSULTATION_EDIT_URL,
  CONSULTATION_DETAILS_URL,
  TEST_TABLE_URL,
  TEST_FORM_URL,
  TEST_EDIT_URL,
  TEST_DETAILS_URL,
  MEDICINE_TABLE_URL,
  MEDICINE_FORM_URL,
  MEDICINE_DETAILS_URL,
  ALLERGY_TABLE_URL,
  ALLERGY_FORM_URL,
  ALLERGY_DETAILS_URL,
  ALLERGY_EDIT_URL,
  MEDICINE_CATEGORY_TABLE_URL,
  MEDICINE_CATEGORY_FORM_URL,
  MEDICINE_CATEGORY_EDIT_URL,
  MEDICINE_CATEGORY_MAPPING_TABLE_URL,
  MEDICINE_CATEGORY_MAPPING_FORM_URL,
  MEDICINE_CATEGORY_MAPPING_EDIT_URL,
  PERMISSIONS_URL,
  PATIENT_TEST_TABLE_URL,
  PATIENT_TEST_FORM_URL,
  PATIENT_TEST_EDIT_URL,
  PATIENT_TEST_DETAILS_URL,
  FINDINGS_URL,
  FINDINGS_FORM_URL,
  FINDINGS_EDIT_URL,
  FINDINGS_DETAILS_URL,
  DOSHA_ANALYSIS_URL,
  // PRAKRITI_URL,
  // VIKRUTI_URL,
  YOGA_ASANA_TABLE_URL,
  YOGA_ASANA_FORM_URL,
  YOGA_ASANA_EDIT_URL,
  YOGA_ASANA_DETAILS_URL,
  INVOICE_URL,
  INVOICE_DETAIL_URL,
  DEPARTMENT_TABLE_URL,
  DEPARTMENT_FORM_URL,
  DEPARTMENT_EDIT_URL,
  DEPARTMENT_DETAILS_URL,
  CONSULTATION_FEES_URL,
  CONSULTATION_FEES_FORM_URL,
  CONSULTATION_FEES_EDIT_URL,
  SURGICAL_HISTORY_TABLE_URL,
  SURGICAL_HISTORY_FORM_URL,
  SURGICAL_HISTORY_EDIT_URL,
  SURGICAL_HISTORY_DETAILS_URL,
  CHIEF_COMPLAINT_URL,
  CHIEF_COMPLAINT_FORM_URL,
  CHIEF_COMPLAINT_EDIT_URL,
  CHIEF_COMPLAINT_DETAILS_URL,
  ON_EXAMINATION_TABLE_URL,
  ON_EXAMINATION_FORM_URL,
  ON_EXAMINATION_EDIT_URL,
  ON_EXAMINATION_DETAILS_URL,
  SERVICE_COST_TABLE_URL,
  SERVICE_COST_FORM_URL,
  SERVICE_COST_EDIT_URL,
  SERVICE_COST_DETAILS_URL,
  AMOUNT_TYPE_EDIT_URL,
  AMOUNT_TYPE_TABLE_URL,
  AMOUNT_TYPE_FORM_URL,
  AMOUNT_TYPE_DETAILS_URL,
  COMORBIDITIES_TABLE_URL,
  COMORBIDITIES_FORM_URL,
  COMORBIDITIES_EDIT_URL,
  COMORBIDITIES_DETAILS_URL,
  DIET_TABLE_URL,
  DIET_FORM_URL,
  DIET_EDIT_URL,
  DIET_DETAILS_URL,
  DIAGNOSIS_TABLE_URL,
  DIAGNOSIS_FORM_URL,
  DIAGNOSIS_EDIT_URL,
  DIAGNOSIS_DETAILS_URL,
} from "@/utils/urls/frontend";
import OpdCaseForm from "./pages/forms/opdForm/opd";
import OpdPage from "./pages/opd/OpdPage";
import Settings from "./pages/settings/Home";
import { USER_PROFILE_URL } from "./utils/urls/backend";
import RoomsPage from "./pages/rooms/RoomsPage";
import RoomsForm from "./pages/forms/roomsForm/rooms";
import AppointmentForm from "./pages/forms/appointmentsForm/appointment";
import { AppointmentPage } from "./pages/appointments/AppointmentPage";
import RolesPage from "./pages/roles";
import RolesForm from "./pages/forms/rolesForm/role";
import RoomDetails from "./pages/rooms/RoomDetail";
import OpdDetail from "./pages/opd/OpdDetailPage";
import AppointmentDetailsPage from "./pages/appointments/AppointmentDetailsPage";
import ConsultationForm from "./pages/forms/consultationForm/consultation";
import { ExaminationsPage } from "./pages/examinations/ExaminationsPage";
import ExaminationDetailsPage from "./pages/examinations/ExaminationDetailsPage";
import ExaminationForm from "./pages/forms/examinationForm/examination";
import ConsultationPage from "./pages/consultation/ConsultationPage";
import ConsultationDetails from "./pages/consultation/ConsultationDetail";
import TestPage from "./pages/test/TestPage";
import TestDetails from "./pages/test/TestDetail";
import MedicinesForm from "./pages/forms/medicinesForm/medicines";
import { MedicinesPage } from "./pages/medicines/MedicinesPage";
import MedicineDetailsPage from "./pages/medicines/MedicineDetailsPage";
import TestForm from "./pages/forms/test/Test";
import AllergyPage from "./pages/allergy/AllergyPage";
import AllergyForm from "./pages/forms/allergy/allergy";
import AllergyDetail from "./pages/allergy/AllergyDetail";
import MedicineCategoryPage from "./pages/medicineCategory/MedicineCategoryPage";
import MedicineCategoryForm from "./pages/forms/medicineCategory/MedicineCategory";
import MedicineCategoryMappingForm from "./pages/forms/medicineCategoryMapping/MedicineCategoryMapping";
import MedicineCategoryMappingPage from "./pages/medicineCategoryMapping/medicineCategoryMappingPage";
import PermissionPage from "./pages/roles/Permission";
import PatientTestPage from "./pages/patien tests/PatientTestPage";
import PatientTestDetails from "./pages/patien tests/PatientTestDetails";
import PatientTestForm from "@/pages/forms/Patient Tests/patientTestForm";
import FindingsPage from "./pages/Findings/FindingsPage";
import FindingsForm from "./pages/forms/findings form/findings";
import FindingDetails from "./pages/Findings/FindingDetail";
import DoshaAnalysisPage from "./pages/doshaAnalysis/DoshaAnalysisPage";
import DoshaAnalysisForm from "./pages/forms/doshaAnalysisForm/doshaAnalysisForm";
import YogaAsanaPage from "./pages/yogaAsana/YogaAsanaPage";
import YogaAsanaForm from "./pages/forms/yogaAsana/yogaAsana";
import YogaAsanaDetail from "./pages/yogaAsana/YogaAsanaDetail";
import InvoicePage from "./pages/invoice/InvoicePage";
import InvoiceDetail from "./pages/invoice/InvoiceDetail";
import DepartmentsPage from "./pages/departments/departments/DepartmentsPage";
import DepartmentForm from "./pages/forms/deparmentsForm/deparmentsForm/departments";
import DepartmentDetails from "./pages/departments/departments/DepartmentDetails";
import ConsultatoinFeesPage from "./pages/consultationFees/ConsultatoinFeesPage";
import ConsultationFeesForm from "./pages/forms/consultation fees form/consultationFeesForm";
import SurgicalHistoryPage from "./pages/surgicalHistory/SurgicalHistoryPage";
import SurgicalHistoryForm from "./pages/forms/surgicalHistory/SurgicalHistory";
import ChiefComplaintPage from "./pages/chiefComplaints/ChiefComplaintPage";
import ChiefComplaintForm from "./pages/forms/chief complaints form/chiefComplaintsForm";
import ChiefComplaintDetail from "./pages/chiefComplaints/ChiefComplaintDetail";
import OnExaminationPage from "./pages/onExamination/OnExaminationPage";
import OnExaminationForms from "./pages/forms/onExamination/OnExamination";
import AmountTypePage from "./pages/amount types/AmountTypePage";
import AmountTypeForm from "./pages/forms/amount type/AmountType";
import AmountTypeDetailsPage from "./pages/amount types/AmountTypeDetailsPage";
import ServiceCostsPage from "./pages/serviceCosts/ServiceCostsPage";
import ServiceCostForm from "./pages/forms/serviceCostForm/serviceCostForm";
import ServiceCostDetail from "./pages/serviceCosts/ServiceCostDetail";
import ComorbiditiesPage from "./pages/comorbidities/ComorbiditiesPage";
import ComorbidityForm from "./pages/forms/comorbidities/Comorbidities";
import ComorbidityDetail from "./pages/comorbidities/ComorbiditiesDetail";
import DietPage from "./pages/diet/DietPage";
import DietForm from "./pages/forms/diet/Diet";
import DiagnosisPage from "./pages/diagnosis/DiagnosisPage";
import DiagnosisForm from "./pages/forms/diagnosis/Diagnosis";
const WithLogin: React.FC<{}> = () => {
  // const doshaPaths = [
  //   DOSHA_ANALYSIS_URL + PRAKRITI_URL,
  //   DOSHA_ANALYSIS_URL + VIKRUTI_URL,
  // ];
  return (
    <Routes>
      <Route
        path={USER_TABLE_URL + USER_URL}
        element={
          <DashboardLayout>
            <Register />
          </DashboardLayout>
        }
      />
      <Route
        path={USER_TABLE_URL + EDIT_USER_URL + "/:id"}
        element={
          <DashboardLayout>
            <Register formType="edit" />
          </DashboardLayout>
        }
      />
      <Route path={FIRST_PAGE_URL} element={<Navigate to={DASHBOARD_URL} />} />
      <Route path={DASHBOARD_URL} element={<Dashboard />} />
      <Route
        path={USER_TABLE_URL}
        element={
          <DashboardLayout>
            <UsersPage />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_TABLE_URL}
        element={
          <DashboardLayout>
            <PatientsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENTS_FORM_URL}
        element={
          <DashboardLayout>
            <PatientAdmissionForm />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_TABLE_URL + PATIENTS_FORM_URL}
        element={
          <DashboardLayout>
            <PatientAdmissionForm />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_TABLE_URL + PATIENTS_FORM_URL + "/:id"}
        element={
          <DashboardLayout>
            <PatientAdmissionForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={SETTINGS_URL}
        element={
          <DashboardLayout>
            <Settings />
          </DashboardLayout>
        }
      />
      <Route
        path={INVOICE_URL}
        element={
          <DashboardLayout>
            <InvoicePage />
          </DashboardLayout>
        }
      />
      <Route
        path={INVOICE_URL + INVOICE_DETAIL_URL + "/:id"}
        element={
          <DashboardLayout>
            <InvoiceDetail />
          </DashboardLayout>
        }
      />
      {/* <Route
        path={ROOMS_TABLE_URL}
        element={
          <DashboardLayout>
            <Settings />
          </DashboardLayout>
        }
      /> */}
      <Route
        path={ROLES_TABLE_URL}
        element={
          <DashboardLayout>
            <RolesPage />
          </DashboardLayout>
        }
      />
      <Route
        path={ROLES_TABLE_URL + ROLES_URL}
        element={
          <DashboardLayout>
            <RolesForm />
          </DashboardLayout>
        }
      />
      <Route
        path={ROLES_TABLE_URL + EDIT_ROLE_URL + "/:id"}
        element={
          <DashboardLayout>
            <RolesForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={USER_TABLE_URL + USER_DETAIL_URL + "/:id"}
        element={
          <DashboardLayout>
            <UserDetailPage />
          </DashboardLayout>
        }
      />
      <Route
        path={USER_PROFILE_URL + "/:id"}
        element={
          <DashboardLayout>
            <Register formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={EDIT_USER_URL + "/:id"}
        element={
          <DashboardLayout>
            <Register formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={USER_PROFILE_URL}
        element={
          <DashboardLayout>
            <UserDetailPage />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_TABLE_URL + PATIENT_DETAIL_URL + "/:id"}
        element={
          <DashboardLayout>
            <PatientDetailsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_DETAIL_URL + "/:id"}
        element={
          <DashboardLayout>
            <PatientDetailsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={OPD_TABLE_URL + OPD_FORM_URL}
        element={
          <DashboardLayout>
            <OpdCaseForm />
          </DashboardLayout>
        }
      />
      <Route
        path={OPD_TABLE_URL + EDIT_OPD_URL + "/:id"}
        element={
          <DashboardLayout>
            <OpdCaseForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={OPD_TABLE_URL + OPD_DETAIL_URL + "/:id"}
        element={
          <DashboardLayout>
            <OpdDetail />
          </DashboardLayout>
        }
      />
      <Route
        path={OPD_TABLE_URL}
        element={
          <DashboardLayout>
            <OpdPage />
          </DashboardLayout>
        }
      />
      <Route
        path={ROOMS_TABLE_URL + ROOMS_FORM_URL}
        element={
          <DashboardLayout>
            <RoomsForm />
          </DashboardLayout>
        }
      />
      <Route
        path={ROOMS_TABLE_URL + ROOMS_FORM_URL + "/:id"}
        element={
          <DashboardLayout>
            <RoomsForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={ROOMS_TABLE_URL + ROOMS_DETAIL_URL + "/:id"}
        element={
          <DashboardLayout>
            <RoomDetails />
          </DashboardLayout>
        }
      />
      <Route
        path={ROOMS_TABLE_URL}
        element={
          <DashboardLayout>
            <RoomsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={APPOINTMENT_TABLE_URL + APPOINTMENT_FORM_URL}
        element={
          <DashboardLayout>
            <AppointmentForm />
          </DashboardLayout>
        }
      />
      <Route
        path={APPOINTMENT_FORM_URL}
        element={
          <DashboardLayout>
            <AppointmentForm />
          </DashboardLayout>
        }
      />
      <Route
        path={APPOINTMENT_TABLE_URL + APPOINTMENT_FORM_URL + "/:id"}
        element={
          <DashboardLayout>
            <AppointmentForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={APPOINTMENT_TABLE_URL}
        element={
          <DashboardLayout>
            <AppointmentPage />
          </DashboardLayout>
        }
      />
      <Route
        path={APPOINTMENT_TABLE_URL + APPOINTMENT_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <AppointmentDetailsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={YOGA_ASANA_TABLE_URL + YOGA_ASANA_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <YogaAsanaDetail />
          </DashboardLayout>
        }
      />
      <Route
        path={DEPARTMENT_TABLE_URL}
        element={
          <DashboardLayout>
            <DepartmentsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={DEPARTMENT_TABLE_URL + DEPARTMENT_FORM_URL}
        element={
          <DashboardLayout>
            <DepartmentForm />
          </DashboardLayout>
        }
      />
      <Route
        path={DEPARTMENT_TABLE_URL + DEPARTMENT_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <DepartmentForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={DEPARTMENT_TABLE_URL + DEPARTMENT_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <DepartmentDetails />
          </DashboardLayout>
        }
      />
      <Route
        path={EXAMINATION_TABLE_URL + EXAMINATION_FORM_URL}
        element={
          <DashboardLayout>
            <ExaminationForm />
          </DashboardLayout>
        }
      />
      <Route
        path={EXAMINATION_TABLE_URL + EXAMINATION_FORM_URL + "/:id"}
        element={
          <DashboardLayout>
            <ExaminationForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={EXAMINATION_TABLE_URL}
        element={
          <DashboardLayout>
            <ExaminationsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={EXAMINATION_TABLE_URL + EXAMINATION_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <ExaminationDetailsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={CONSULTATION_TABLE_URL + CONSULTATION_FORM_URL}
        element={
          <DashboardLayout>
            <ConsultationForm />
          </DashboardLayout>
        }
      />
      <Route
        path={CONSULTATION_TABLE_URL + CONSULTATION_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <ConsultationForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={CONSULTATION_TABLE_URL}
        element={
          <DashboardLayout>
            <ConsultationPage />
          </DashboardLayout>
        }
      />
      <Route
        path={CONSULTATION_TABLE_URL + CONSULTATION_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <ConsultationDetails />
          </DashboardLayout>
        }
      />

      <Route
        path={MEDICINE_TABLE_URL + MEDICINE_FORM_URL}
        element={
          <DashboardLayout>
            <MedicinesForm />
          </DashboardLayout>
        }
      />
      <Route
        path={MEDICINE_TABLE_URL + MEDICINE_FORM_URL + "/:id"}
        element={
          <DashboardLayout>
            <MedicinesForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={MEDICINE_TABLE_URL}
        element={
          <DashboardLayout>
            <MedicinesPage />
          </DashboardLayout>
        }
      />
      <Route
        path={MEDICINE_TABLE_URL + MEDICINE_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <MedicineDetailsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={TEST_TABLE_URL}
        element={
          <DashboardLayout>
            <TestPage />
          </DashboardLayout>
        }
      />
      <Route
        path={TEST_TABLE_URL + TEST_FORM_URL}
        element={
          <DashboardLayout>
            <TestForm />
          </DashboardLayout>
        }
      />
      <Route
        path={TEST_TABLE_URL + TEST_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <TestForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={TEST_TABLE_URL + TEST_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <TestDetails />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_TEST_TABLE_URL}
        element={
          <DashboardLayout>
            <PatientTestPage />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_TEST_TABLE_URL + PATIENT_TEST_FORM_URL}
        element={
          <DashboardLayout>
            <PatientTestForm />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_TEST_TABLE_URL + PATIENT_TEST_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <PatientTestForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={PATIENT_TEST_TABLE_URL + PATIENT_TEST_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <PatientTestDetails />
          </DashboardLayout>
        }
      />

      <Route
        path={ALLERGY_TABLE_URL}
        element={
          <DashboardLayout>
            <AllergyPage />
          </DashboardLayout>
        }
      />
      <Route
        path={ALLERGY_TABLE_URL + ALLERGY_FORM_URL}
        element={
          <DashboardLayout>
            <AllergyForm />
          </DashboardLayout>
        }
      />
      <Route
        path={ALLERGY_TABLE_URL + ALLERGY_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <AllergyForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={ALLERGY_TABLE_URL + ALLERGY_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <AllergyDetail />
          </DashboardLayout>
        }
      />

      <Route
        path={MEDICINE_CATEGORY_TABLE_URL}
        element={
          <DashboardLayout>
            <MedicineCategoryPage />
          </DashboardLayout>
        }
      />
      <Route
        path={MEDICINE_CATEGORY_TABLE_URL + MEDICINE_CATEGORY_FORM_URL}
        element={
          <DashboardLayout>
            <MedicineCategoryForm />
          </DashboardLayout>
        }
      />
      <Route
        path={MEDICINE_CATEGORY_TABLE_URL + MEDICINE_CATEGORY_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <MedicineCategoryForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={MEDICINE_CATEGORY_MAPPING_TABLE_URL}
        element={
          <DashboardLayout>
            <MedicineCategoryMappingPage />
          </DashboardLayout>
        }
      />
      <Route
        path={
          MEDICINE_CATEGORY_MAPPING_TABLE_URL +
          MEDICINE_CATEGORY_MAPPING_FORM_URL
        }
        element={
          <DashboardLayout>
            <MedicineCategoryMappingForm />
          </DashboardLayout>
        }
      />
      <Route
        path={
          MEDICINE_CATEGORY_MAPPING_TABLE_URL +
          MEDICINE_CATEGORY_MAPPING_EDIT_URL +
          "/:id"
        }
        element={
          <DashboardLayout>
            <MedicineCategoryMappingForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={ROLES_TABLE_URL + PERMISSIONS_URL}
        element={
          <DashboardLayout>
            <PermissionPage />
          </DashboardLayout>
        }
      />

      <Route
        path={FINDINGS_URL}
        element={
          <DashboardLayout>
            <FindingsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={FINDINGS_URL + FINDINGS_FORM_URL}
        element={
          <DashboardLayout>
            <FindingsForm />
          </DashboardLayout>
        }
      />
      <Route
        path={FINDINGS_URL + FINDINGS_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <FindingsForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={FINDINGS_URL + FINDINGS_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <FindingDetails />
          </DashboardLayout>
        }
      />

      <Route
        path={DOSHA_ANALYSIS_URL + "/:type"}
        element={
          <DashboardLayout>
            <DoshaAnalysisPage />
          </DashboardLayout>
        }
      />
      <Route
        path={DOSHA_ANALYSIS_URL + "/:type" + "/add"}
        element={
          <DashboardLayout>
            <DoshaAnalysisForm />
          </DashboardLayout>
        }
      />
      <Route
        path={DOSHA_ANALYSIS_URL + "/:type" + "/:id"}
        element={
          <DashboardLayout>
            <DoshaAnalysisForm formType="edit" />
            {/* null */}
          </DashboardLayout>
        }
      />
      <Route
        path={YOGA_ASANA_TABLE_URL}
        element={
          <DashboardLayout>
            <YogaAsanaPage />
          </DashboardLayout>
        }
      />
      <Route
        path={YOGA_ASANA_TABLE_URL + YOGA_ASANA_FORM_URL}
        element={
          <DashboardLayout>
            <YogaAsanaForm />
          </DashboardLayout>
        }
      />
      <Route
        path={YOGA_ASANA_TABLE_URL + YOGA_ASANA_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <YogaAsanaForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={YOGA_ASANA_TABLE_URL + YOGA_ASANA_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <AppointmentDetailsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={SURGICAL_HISTORY_TABLE_URL}
        element={
          <DashboardLayout>
            <SurgicalHistoryPage />
          </DashboardLayout>
        }
      />
      <Route
        path={SURGICAL_HISTORY_TABLE_URL + SURGICAL_HISTORY_FORM_URL}
        element={
          <DashboardLayout>
            <SurgicalHistoryForm />
          </DashboardLayout>
        }
      />
      <Route
        path={SURGICAL_HISTORY_TABLE_URL + SURGICAL_HISTORY_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <SurgicalHistoryForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={
          SURGICAL_HISTORY_TABLE_URL + SURGICAL_HISTORY_DETAILS_URL + "/:id"
        }
        element={
          <DashboardLayout>
            <AppointmentDetailsPage />
          </DashboardLayout>
        }
      />

      {/* <Route
        path={DOSHA_ANALYSIS_URL + PRAKRITI_URL}
        element={
          <DashboardLayout>
            <DoshaAnalysisPage />
          </DashboardLayout>
        }
      />
      <Route
        path={DOSHA_ANALYSIS_URL + VIKRUTI_URL}
        element={
          <DashboardLayout>
            <DoshaAnalysisPage />
          </DashboardLayout>
        }
      /> */}

      <Route
        path={CONSULTATION_FEES_URL}
        element={
          <DashboardLayout>
            <ConsultatoinFeesPage />
          </DashboardLayout>
        }
      />
      <Route
        path={CONSULTATION_FEES_URL + CONSULTATION_FEES_FORM_URL}
        element={
          <DashboardLayout>
            <ConsultationFeesForm />
          </DashboardLayout>
        }
      />
      <Route
        path={CONSULTATION_FEES_URL + CONSULTATION_FEES_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <ConsultationFeesForm formType="edit" />
          </DashboardLayout>
        }
      />

      <Route
        path={CHIEF_COMPLAINT_URL}
        element={
          <DashboardLayout>
            <ChiefComplaintPage />
          </DashboardLayout>
        }
      />
      <Route
        path={CHIEF_COMPLAINT_URL + CHIEF_COMPLAINT_FORM_URL}
        element={
          <DashboardLayout>
            <ChiefComplaintForm />
          </DashboardLayout>
        }
      />
      <Route
        path={CHIEF_COMPLAINT_URL + CHIEF_COMPLAINT_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <ChiefComplaintForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={CHIEF_COMPLAINT_URL + CHIEF_COMPLAINT_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <ChiefComplaintDetail />
          </DashboardLayout>
        }
      />
      <Route
        path={ON_EXAMINATION_TABLE_URL}
        element={
          <DashboardLayout>
            <OnExaminationPage />
          </DashboardLayout>
        }
      />

      <Route
        path={AMOUNT_TYPE_TABLE_URL}
        element={
          <DashboardLayout>
            <AmountTypePage />
          </DashboardLayout>
        }
      />

      <Route
        path={ON_EXAMINATION_TABLE_URL + ON_EXAMINATION_FORM_URL}
        element={
          <DashboardLayout>
            <OnExaminationForms />
          </DashboardLayout>
        }
      />
      <Route
        path={ON_EXAMINATION_TABLE_URL + ON_EXAMINATION_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <OnExaminationForms formType="edit" />
          </DashboardLayout>
        }
      />
      {/* <Route
        path={ON_EXAMINATION_TABLE_URL + ON_EXAMINATION_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <OnExaminationDetails />
          </DashboardLayout>
        }
      /> */}
      <Route
        path={AMOUNT_TYPE_TABLE_URL + AMOUNT_TYPE_FORM_URL}
        element={
          <DashboardLayout>
            <AmountTypeForm />
          </DashboardLayout>
        }
      />
      <Route
        path={ON_EXAMINATION_TABLE_URL + ON_EXAMINATION_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <OnExaminationForms formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={AMOUNT_TYPE_TABLE_URL + AMOUNT_TYPE_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <AmountTypeForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={ON_EXAMINATION_TABLE_URL + ON_EXAMINATION_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <ChiefComplaintDetail />
          </DashboardLayout>
        }
      />
      <Route
        path={AMOUNT_TYPE_TABLE_URL + AMOUNT_TYPE_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <AmountTypeDetailsPage />
          </DashboardLayout>
        }
      />

      <Route
        path={SERVICE_COST_TABLE_URL}
        element={
          <DashboardLayout>
            <ServiceCostsPage />
          </DashboardLayout>
        }
      />
      <Route
        path={SERVICE_COST_TABLE_URL + SERVICE_COST_FORM_URL}
        element={
          <DashboardLayout>
            <ServiceCostForm />
          </DashboardLayout>
        }
      />
      <Route
        path={SERVICE_COST_TABLE_URL + SERVICE_COST_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <ServiceCostForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={SERVICE_COST_TABLE_URL + SERVICE_COST_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <ServiceCostDetail />
          </DashboardLayout>
        }
      />
      <Route
        path={DIAGNOSIS_TABLE_URL}
        element={
          <DashboardLayout>
            <DiagnosisPage />
          </DashboardLayout>
        }
      />
      <Route
        path={DIAGNOSIS_TABLE_URL + DIAGNOSIS_FORM_URL}
        element={
          <DashboardLayout>
            <DiagnosisForm />
          </DashboardLayout>
        }
      />
      <Route
        path={DIAGNOSIS_TABLE_URL + DIAGNOSIS_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <DiagnosisForm formType="edit" />
          </DashboardLayout>
        }
      />
      {/* <Route
        path={DIAGNOSIS_TABLE_URL + DIAGNOSIS_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <ServiceCostDetail />
          </DashboardLayout>
        }
      /> */}
      <Route
        path={COMORBIDITIES_TABLE_URL}
        element={
          <DashboardLayout>
            <ComorbiditiesPage />
          </DashboardLayout>
        }
      />

      <Route
        path={DIET_TABLE_URL}
        element={
          <DashboardLayout>
            <DietPage />
          </DashboardLayout>
        }
      />
      <Route
        path={COMORBIDITIES_TABLE_URL + COMORBIDITIES_FORM_URL}
        element={
          <DashboardLayout>
            <ComorbidityForm />
          </DashboardLayout>
        }
      />
      <Route
        path={DIET_TABLE_URL + DIET_FORM_URL}
        element={
          <DashboardLayout>
            <DietForm />
          </DashboardLayout>
        }
      />
      <Route
        path={COMORBIDITIES_TABLE_URL + COMORBIDITIES_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <ComorbidityForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={DIET_TABLE_URL + DIET_EDIT_URL + "/:id"}
        element={
          <DashboardLayout>
            <DietForm formType="edit" />
          </DashboardLayout>
        }
      />
      <Route
        path={COMORBIDITIES_TABLE_URL + COMORBIDITIES_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            <ComorbidityDetail />
          </DashboardLayout>
        }
      />
      <Route
        path={DIET_TABLE_URL + DIET_DETAILS_URL + "/:id"}
        element={
          <DashboardLayout>
            {/* <DietDetail /> */}
            nulll
          </DashboardLayout>
        }
      />

      <Route path="*" element={<NotFound />} />
    </Routes>
  );
};

export default WithLogin;
