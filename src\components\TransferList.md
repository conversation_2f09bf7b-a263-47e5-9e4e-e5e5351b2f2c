# TransferList Component

A modern, accessible, and feature-rich dual-list selector component built with React and Tailwind CSS.

## Features

✅ **Search Functionality** - Real-time filtering with label and description matching  
✅ **User Friendly** - Intuitive controls with visual feedback  
✅ **Responsive Design** - Adapts to all screen sizes  
✅ **Premium Look** - Modern design with smooth animations  
✅ **Accessibility** - Full keyboard navigation and screen reader support  
✅ **Form Integration** - Works seamlessly with forms and validation  
✅ **Customizable** - Extensive props for styling and behavior  
✅ **Select All/Deselect All** - Bulk operations for efficiency  

## Basic Usage

```tsx
import TransferList from './components/TransferList';

const options = [
  { value: 'option1', label: 'Option 1', description: 'Description for option 1' },
  { value: 'option2', label: 'Option 2', description: 'Description for option 2' },
  { value: 'option3', label: 'Option 3', disabled: true },
];

function MyComponent() {
  const [selectedValues, setSelectedValues] = useState([]);

  return (
    <TransferList
      label="Select Options"
      options={options}
      value={selectedValues}
      onChange={setSelectedValues}
      availableTitle="Available Options"
      selectedTitle="Selected Options"
      height="300px"
      searchable={true}
      showSelectAll={true}
      showItemCount={true}
    />
  );
}
```

## Props

### Core Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `options` | `Option[]` | `[]` | Array of available options |
| `value` | `(string \| number)[]` | `[]` | Currently selected values |
| `onChange` | `(values: (string \| number)[]) => void` | - | Callback when selection changes |

### Customization Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Label for the component |
| `availableTitle` | `string` | `"Available Options"` | Title for available options list |
| `selectedTitle` | `string` | `"Selected Options"` | Title for selected options list |
| `height` | `string` | `"300px"` | Height of the option lists |
| `maxSelections` | `number` | - | Maximum number of selections allowed |

### Feature Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `searchable` | `boolean` | `true` | Enable search functionality |
| `showSelectAll` | `boolean` | `true` | Show select all/deselect all buttons |
| `showItemCount` | `boolean` | `true` | Show item count badges |

### Form Integration Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | `string` | - | Form field name |
| `id` | `string` | - | Form field ID |
| `required` | `boolean` | `false` | Mark field as required |
| `disabled` | `boolean` | `false` | Disable the component |
| `error` | `string` | - | Error message to display |

### Styling Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `className` | `string` | `""` | Additional CSS classes |
| `compact` | `boolean` | `false` | Use compact spacing |

### Accessibility Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `aria-label` | `string` | - | ARIA label for the component |
| `aria-describedby` | `string` | - | ARIA described by reference |

## Option Interface

```tsx
interface Option {
  value: string | number;
  label: string;
  description?: string;
  disabled?: boolean;
}
```

## Advanced Examples

### With Maximum Selections
```tsx
<TransferList
  options={options}
  value={selectedValues}
  onChange={setSelectedValues}
  maxSelections={5}
  label="Select up to 5 skills"
/>
```

### Compact Mode
```tsx
<TransferList
  options={options}
  value={selectedValues}
  onChange={setSelectedValues}
  compact={true}
  height="200px"
  showSelectAll={false}
/>
```

### Form Integration
```tsx
<form onSubmit={handleSubmit}>
  <TransferList
    name="skills"
    id="skills"
    required={true}
    options={skillOptions}
    value={formData.skills}
    onChange={(values) => setFormData(prev => ({ ...prev, skills: values }))}
    error={errors.skills}
  />
  <button type="submit">Submit</button>
</form>
```

## Keyboard Navigation

- **Tab/Shift+Tab**: Navigate between elements
- **Space/Enter**: Select/deselect items
- **Ctrl/Cmd+Click**: Multi-select items
- **Double-click**: Move items between lists

## Styling

The component uses Tailwind CSS classes and supports dark mode out of the box. You can customize the appearance by:

1. **Custom CSS classes**: Use the `className` prop
2. **Tailwind utilities**: Override default styles
3. **CSS variables**: Define custom color schemes

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- React 16.8+
- Lucide React (for icons)
- Tailwind CSS 3.0+

## License

MIT License - feel free to use in your projects!
