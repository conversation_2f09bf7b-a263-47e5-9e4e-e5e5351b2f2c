# TransferList Component

A modern, accessible, and feature-rich dual-list selector component built with React and Tailwind CSS. **Completely reusable and generic** - works with any data structure.

## Features

✅ **Generic & Reusable** - Works with any data structure using flexible interface
✅ **Drag & Drop** - Intuitive drag and drop between lists with visual feedback
✅ **Custom Values** - Add custom items with name, description, and value properties
✅ **Value Property** - Includes value field alongside name and description for forms
✅ **Search Functionality** - Real-time filtering across all item properties
✅ **Compact Design** - Reduced height with side-by-side layout
✅ **Content Wrapping** - Handles long text gracefully with proper wrapping
✅ **Reset Control** - Simple reset functionality to clear selections
✅ **Responsive Design** - Adapts to all screen sizes
✅ **Accessibility** - Full keyboard navigation and screen reader support
✅ **Form Integration** - Works seamlessly with forms and validation
✅ **Premium Look** - Modern design with smooth animations

## Basic Usage

```tsx
import TransferList from './components/TransferList';

const items = [
  {
    id: 1,
    name: 'Item 1',
    description: 'Description for item 1',
    status: 'active'
  },
  {
    id: 2,
    name: 'Item 2',
    subtitle: 'Additional info',
    badge: 'New'
  },
  {
    id: 3,
    name: 'Item 3',
    disabled: true
  },
];

function MyComponent() {
  const [selectedItems, setSelectedItems] = useState([]);

  return (
    <TransferList
      label="Select Items"
      sourceData={items}
      selectedItems={selectedItems}
      onSelectionChange={setSelectedItems}
      sourceTitle="Available Items"
      selectedTitle="Selected Items"
      height="200px"
      searchable={true}
      showCount={true}
    />
  );
}
```

## Props

### Core Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `options` | `Option[]` | `[]` | Array of available options |
| `value` | `(string \| number)[]` | `[]` | Currently selected values |
| `onChange` | `(values: (string \| number)[]) => void` | - | Callback when selection changes |

### Customization Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | - | Label for the component |
| `availableTitle` | `string` | `"Available Options"` | Title for available options list |
| `selectedTitle` | `string` | `"Selected Options"` | Title for selected options list |
| `height` | `string` | `"200px"` | Height of the option lists |
| `maxSelections` | `number` | - | Maximum number of selections allowed |

### Feature Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `searchable` | `boolean` | `true` | Enable search functionality |
| `showSelectAll` | `boolean` | `true` | Show select all/deselect all buttons |
| `showItemCount` | `boolean` | `true` | Show item count badges |
| `enableDragDrop` | `boolean` | `true` | Enable drag and drop functionality |
| `allowCustomValues` | `boolean` | `false` | Allow adding custom items |
| `customValuePlaceholder` | `string` | `"Add custom item..."` | Placeholder for custom value button |
| `onCustomValueAdd` | `function` | - | Custom handler for creating new items |

### Form Integration Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `name` | `string` | - | Form field name |
| `id` | `string` | - | Form field ID |
| `required` | `boolean` | `false` | Mark field as required |
| `disabled` | `boolean` | `false` | Disable the component |
| `error` | `string` | - | Error message to display |

### Styling Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `className` | `string` | `""` | Additional CSS classes |
| `compact` | `boolean` | `false` | Use compact spacing |

### Accessibility Props
| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `aria-label` | `string` | - | ARIA label for the component |
| `aria-describedby` | `string` | - | ARIA described by reference |

## Item Interface

```tsx
interface TransferListItem {
  id: string | number;           // Required: Unique identifier
  name: string;                  // Required: Display name
  value?: string | number;       // Optional: Value for form submission
  label?: string;                // Optional: Alternative display text
  description?: string;          // Optional: Secondary text (email, etc.)
  subtitle?: string;             // Optional: Additional info line
  status?: string;               // Optional: Status for badge styling
  badge?: string;                // Optional: Badge text
  isCustom?: boolean;           // Optional: Mark as custom item
  disabled?: boolean;           // Optional: Disable selection
  [key: string]: any;           // Optional: Any additional properties
}
```

### Property Usage:
- **`name`**: Main display text (always shown)
- **`value`**: Form submission value (defaults to name if not provided)
- **`description`**: Secondary text (email, phone, etc.)
- **`subtitle`**: Additional context (department, role, etc.)
- **`status`**: Used for status badge coloring (active/inactive/etc.)
- **`badge`**: Custom badge text
- **`disabled`**: Prevents selection and drag/drop
- **`isCustom`**: Shows "Custom" badge for user-added items
- **`[key: string]: any`**: Any additional properties for custom use cases

## Real-World Examples

### 1. User Management
```tsx
const users = [
  {
    id: 1,
    name: 'John Doe',
    description: '<EMAIL>',
    subtitle: 'Engineering • Senior Developer',
    status: 'active'
  },
  {
    id: 2,
    name: 'Jane Smith',
    description: '<EMAIL>',
    subtitle: 'Marketing • Manager',
    status: 'inactive'
  }
];

<TransferList
  label="Assign Team Members"
  sourceData={users}
  selectedItems={selectedUsers}
  onSelectionChange={setSelectedUsers}
  maxSelections={10}
  height="250px"
/>
```

### 2. Product Categories
```tsx
const categories = [
  {
    id: 'electronics',
    name: 'Electronics',
    description: 'Phones, laptops, accessories',
    badge: 'Popular'
  },
  {
    id: 'clothing',
    name: 'Clothing',
    description: 'Shirts, pants, shoes',
    status: 'active'
  }
];

<TransferList
  label="Select Categories"
  sourceData={categories}
  selectedItems={selectedCategories}
  onSelectionChange={setSelectedCategories}
  sourceTitle="All Categories"
  selectedTitle="Active Categories"
/>
```

### 3. Simple Options
```tsx
const options = [
  { id: 1, name: 'Option A' },
  { id: 2, name: 'Option B' },
  { id: 3, name: 'Option C', disabled: true }
];

<TransferList
  sourceData={options}
  selectedItems={selected}
  onSelectionChange={setSelected}
  height="150px"
/>
```

### 4. With Drag & Drop and Custom Values
```tsx
const handleCustomItem = (name, description) => ({
  id: `custom_${Date.now()}`,
  name: name,
  value: name.toLowerCase().replace(/\s+/g, '_'),
  description: description || `${name} (Custom)`,
  isCustom: true
});

<TransferList
  label="Team Members"
  sourceData={teamData}
  selectedItems={selectedTeam}
  onSelectionChange={setSelectedTeam}
  enableDragDrop={true}
  allowCustomValues={true}
  customValuePlaceholder="Add Custom Member"
  onCustomValueAdd={handleCustomItem}
  maxSelections={10}
/>
```

### 5. Form Integration
```tsx
<form onSubmit={handleSubmit}>
  <TransferList
    label="Required Skills"
    sourceData={skillsData}
    selectedItems={formData.skills}
    onSelectionChange={(items) => setFormData(prev => ({ ...prev, skills: items }))}
    required={true}
    error={errors.skills}
    maxSelections={5}
    enableDragDrop={true}
    allowCustomValues={true}
  />
  <button type="submit">Submit</button>
</form>
```

## Drag & Drop

The component supports intuitive drag and drop functionality:

- **Drag items** from one list to another to move them
- **Visual feedback** shows drop zones with highlighting
- **Automatic selection** - dragged items are automatically selected/deselected
- **Disabled items** cannot be dragged
- **Touch support** for mobile devices

## Custom Values

Add custom items dynamically:

- **Click "Add Custom"** button in the selected items section
- **Enter name and description** in the input fields
- **Custom handler** allows full control over item creation
- **Automatic marking** with "Custom" badge
- **Form integration** with proper value assignment

## Keyboard Navigation

- **Tab/Shift+Tab**: Navigate between elements
- **Space/Enter**: Select/deselect items
- **Arrow Keys**: Navigate through items
- **Reset Button**: Clear all selections

## Styling

The component uses Tailwind CSS classes and supports dark mode out of the box. You can customize the appearance by:

1. **Custom CSS classes**: Use the `className` prop
2. **Tailwind utilities**: Override default styles
3. **CSS variables**: Define custom color schemes

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

- React 16.8+
- Lucide React (for icons)
- Tailwind CSS 3.0+

## License

MIT License - feel free to use in your projects!
