import Button from "@/components/button";
import Input from "@/components/input";
import Text from "@/components/text";
import View from "@/components/view";
import { Test } from "@/interfaces/test";
import useForm from "@/utils/custom-hooks/use-form";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { validationForm } from "./validationForm";
import { toast } from "@/utils/custom-hooks/use-toast";
import { useTest } from "@/actions/calls/test";
import { FormTypeProps } from "@/interfaces/dashboard";
import { useDispatch, useSelector } from "react-redux";
import { clearTestDetailSlice } from "@/actions/slices/test";
import TipTapTextEditor from "@/components/TipTapTexteditor";

const TestForm: React.FC<FormTypeProps> = ({ formType = "add" }) => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { addTestHandler, testEditHandler, testDetailHandler, cleanUp } =
    useTest();

  useEffect(() => {
    if (formType === "edit" && id) {
      testDetailHandler(id, () => {});
    }
    return () => {
      cleanUp();
      dispatch(clearTestDetailSlice());
    };
  }, [id, formType]);

  const testData = useSelector((state: any) => state.test.testDetailData);
  const { values, handleChange, handleTipTapChange } = useForm<Test | null>(
    testData
  );
  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    let testFormObj: Partial<Test> = {};
    try {
      for (let [key, value] of formData.entries()) {
        testFormObj[key as keyof Test] = value as any;
      }
      await validationForm.validate(testFormObj, { abortEarly: false });
      setErrors({});
      setIsSubmitting(true);
      if (formType === "add") {
        addTestHandler(testFormObj, (success: boolean) => {
          if (success) {
            navigate(-1);
            toast({
              title: "Success!",
              description: "Test Added successfully.",
              variant: "success",
            });
          } else {
            setIsSubmitting(false);
            toast({
              title: "Error!",
              description: "Failed to add Test",
              variant: "destructive",
            });
          }
        });
      } else if (id) {
        testEditHandler(id, testFormObj, (success: boolean) => {
          if (success) {
            navigate(-1);
            toast({
              title: "Success!",
              description: "Test Updated successfully.",
              variant: "success",
            });
          } else {
            setIsSubmitting(false);
            toast({
              title: "Error!",
              description: "Failed to update Test",
              variant: "destructive",
            });
          }
        });
      }
    } catch (error: any) {
      console.error("Validation Error:", error);
      setIsSubmitting(false);
      if (error.inner) {
        const validationErrors: Record<string, string> = {};
        error.inner.forEach((e: any) => {
          validationErrors[e.path] = e.message;
        });
        setErrors(validationErrors);
      }
    }
  };
  return (
    <View className="min-h-screen dark:bg-background flex flex-col  items-center p-4">
      <View className="border border-border dark:bg-card rounded-lg shadow-card w-full max-w-4xl p-6 md:p-8 mb-8">
        <View className=" flex items-center justify-between">
          <Text
            as="h2"
            weight="font-bold"
            className="text-2xl font-bold text-center text-primary mb-1"
          >
            Test Form
          </Text>

          <Button onPress={() => navigate(-1)} variant="outline">
            Back to Home
          </Button>
        </View>
        <Text as="p" className="text-text-light text-left mb-6">
          {/* {formType === "add" && "Fill in the details to create a new account"} */}
          Fill in the details to create a Test
        </Text>
        <form onSubmit={handleSubmit}>
          <View className="grid grid-cols-1 gap-4 mb-4">
            <View>
              <Input
                required={true}
                id="test_name"
                name="test_name"
                label="Test Name"
                onChange={handleChange}
                error={errors?.test_name}
                value={values?.test_name}
                placeholder="Test Name"
              />
            </View>
            <View>
              <Input
                required={true}
                id="test_price"
                name="test_price"
                label="Test Amount"
                onChange={handleChange}
                error={errors?.test_price}
                value={values?.test_price}
                placeholder="Test Name"
              />
            </View>
            <View>
              <Input
                required={true}
                id="tax_price"
                name="tax_price"
                label="Tax Amount"
                onChange={handleChange}
                error={errors?.tax_price}
                value={values?.tax_price}
                placeholder="Test Name"
              />
            </View>
            <View>
              <TipTapTextEditor
                required={true}
                name="test_description"
                label="Test Description"
                onChange={handleTipTapChange}
                areaHeight="h-24"
                error={errors?.test_description}
                value={values?.test_description}
                placeholder="Test Description"
              />
            </View>
          </View>
          <View className="col-span-2 mt-6">
            <Button
              htmlType="submit"
              loading={isSubmitting}
              className="w-full bg-primary text-white rounded-md py-3 font-medium hover:bg-primary-600 transition focus:outline-none focus:ring-2 focus:ring-primary-300 focus:ring-offset-2"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </Button>
          </View>
        </form>
      </View>
    </View>
  );
};
export default TestForm;
