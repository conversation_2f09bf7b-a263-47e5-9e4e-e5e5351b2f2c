import * as Yup from "yup";

export const validationForm = Yup.object({
  service_name: Yup.string().required("Consultation Name is required"),
  cost: Yup.string()
    .required("Consultation Amount is required")
    .test("is-valid-amount", "Amount can only contain numbers", function (value) {
      if (!value) return true;
      return !isNaN(Number(value));
    }),
    description: Yup.string().nullable(),
  status: Yup.string().required("Status is required"),

});

