import View from "@/components/view";
import Text from "@/components/text";
import <PERSON><PERSON> from "@/components/button";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { useParams } from "react-router-dom";
import React, { useState, useEffect } from "react";
import { usePatient } from "@/actions/calls/patient";
import { toast } from "@/utils/custom-hooks/use-toast";
import {
  DOWNLOAD_PATIENT_FILES_URL,
  DOWNLOAD_ANESTHESIA_FILES_URL,
} from "@/utils/urls/backend";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Download,
  Calendar,
  Clock,
  Stethoscope,
  Plus,
} from "lucide-react";
import { Link } from "react-router-dom";
import {
  APPOINTMENT_FORM_URL,
  CONSULTATION_DETAILS_URL,
  CONSULTATION_TABLE_URL,
  USER_DETAIL_URL,
  USER_TABLE_URL,
} from "@/utils/urls/frontend";
import { useNavigate } from "react-router-dom";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import dayjs from "dayjs";
import PatientInfo from "./PatientInfo";

const PatientDetailsPage = () => {
  const { id } = useParams();
  const { patientDetailHandler, downloadPatientHandler } = usePatient();

  const patient = useSelector(
    (state: RootState) => state?.patient?.patientDetailData
  );

  const navigate = useNavigate();

  const [, setLoading] = useState(true);

  // const [readOnly, setReadOnly] = useState(true);
  const currentSymbol = useSelector(
    (state: RootState) => state.systemSettings.settings.currency_symbol
  );
  // const { appointmentDetailHandler } = useAppointments();

  // const appointmentDetails = useSelector(
  //   (state: RootState) => state.appointment.appointmentDetailData
  // );

  useEffect(() => {
    if (id) {
      patientDetailHandler(id, () => {
        setLoading(false);
      });
    }
  }, [id]);

  // useEffect(() => {
  //     if (params.id) {
  //       appointmentDetailHandler(params.id, () => {});
  //     }
  //   }, [params.id]);

  // if (loading || !patient) {
  //   return (
  //     <View className="text-center text-muted py-10">
  //       Loading patient data...
  //     </View>
  //   );
  // }

  const handleDownload = (path: string) => {
    if (id) {
      downloadPatientHandler(id, path, async (success: boolean) => {
        if (success) {
          toast({
            title: "Success!",
            description: "Successfully downloaded patient details",
            variant: "success",
          });
        } else {
          toast({
            title: "Error",
            description: "Failed to download patient details",
            variant: "destructive",
          });
        }
      });
    }
  };

  return (
    <React.Fragment>
      <View className="space-y-6">
        <View className="flex justify-between items-center">
          <View>
            <Text
              as="h1"
              weight="font-semibold"
              className="text-2xl font-bold text-text-DEFAULT"
            >
              Patient Details
            </Text>
            <Text as="p" className="text-text-light">
              View and manage patient information
            </Text>
          </View>
          <View className="flex space-x-2">
            {/* <button className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-600 transition-colors">
              Edit Patient
            </button> */}
            <Button
              variant="outline"
              onClick={() => navigate(-1)}
              className="flex justify-center items-center gap-2"
            >
              Back to Home
            </Button>
            <Button
              style={{ alignItems: "center" }}
              onClick={() => handleDownload(DOWNLOAD_PATIENT_FILES_URL)}
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-600 transition-colors flex item-center gap-2"
            >
              <Download /> <Text>Patient Files</Text>
            </Button>
            <Button
              style={{ alignItems: "center" }}
              onClick={() => handleDownload(DOWNLOAD_ANESTHESIA_FILES_URL)}
              className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-600 transition-colors flex item-center gap-2"
            >
              <Download /> <Text>Anaesthesia Files</Text>
            </Button>
            {/* <button className="border border-neutral-300 text-text-DEFAULT px-4 py-2 rounded-md hover:bg-neutral-100 transition-colors">
              Schedule Appointment
            </button> */}
          </View>
        </View>

        {/* Patient Summary Card */}
        <PatientInfo patient={patient} />
       
      </View>
      {/* 
       <View className="flex justify-between items-center mt-4">
                          <View>
                            <Text as="h2" weight="font-semibold" className="text-2xl font-bold">
                              Appointments
                            </Text>
                          </View>
                        </View> */}
      <View className="flex justify-between items-center mt-8">
        <Text as="h2" weight="font-semibold" className="!text-2xl font-bold">
          Appointments
        </Text>
        <Button
          variant="primary"
          size="small"
          onPress={() => {
            navigate(`${APPOINTMENT_FORM_URL}?patientId=${patient?.id}`);
          }}
          className="flex items-center gap-2"
        >
          <Plus size={16} />
          Add Appointment
        </Button>
      </View>
      {patient?.appointments?.map((data: any, index: number) => {
        return (
          <View key={data.id} className="space-y-6 py-8 border-b">
            <Card>
              <View className="flex justify-between items-center mx-4 mb-6 pt-6">
                <View className="flex !items-center gap-2">
                  <Text
                    as="h2"
                    className="text-lg font-bold flex items-center gap-2"
                  >
                    <View className="flex items-center">
                      Appointment #
                      <Text as="h2" className="text-muted-foreground">
                        {data?.appointment_number}
                      </Text>
                    </View>
                  </Text>
                  {patient?.consultation && (
                    <>
                      {" -> "}
                      <Text as="span">
                        <Link
                          target="_blank"
                          className="text-primary hover:underline"
                          to={
                            CONSULTATION_TABLE_URL +
                            CONSULTATION_DETAILS_URL +
                            "/" +
                            patient?.consultation[index]?.id
                          }
                        >
                          Consultation ({index + 1})
                        </Link>
                      </Text>
                    </>
                  )}

                  {data?.doctor_name && (
                    <>
                      {" -> "}
                      <Text as="span">
                        <Link
                          target="_blank"
                          className=" flex  text-primary hover:underline"
                          to={
                            USER_TABLE_URL +
                            USER_DETAIL_URL +
                            "/" +
                            data?.doctor_id
                          }
                        >
                          <Stethoscope className="h-5 w-5 text-primary mr-2" />
                          <span>{data?.doctor_name}</span>
                        </Link>
                      </Text>
                    </>
                  )}
                </View>

                <Text
                  as="span"
                  className={`px-3 py-1 rounded-full text-xs   font-medium `}
                  style={getStatusColorScheme(data?.status)}
                >
                  {data?.status}
                </Text>
              </View>

              {/* <AppointmentIndex readOnly appointmentDetails={data} showPatientDetails={false} usingAppointmentCardStyle={false} /> */}
              <View>
                {/* {!readOnly && (
                        <View className="flex justify-between items-center">
                          <View>
                            <Text as="h1" weight="font-semibold" className="text-2xl font-bold">
                              Appointment Details
                            </Text>
                            <Text as="p" className="text-muted-foreground">
                              View and manage appointment information
                            </Text>
                          </View>
                          <View className="flex space-x-2">
                            <Button variant="outline" onPress={() => navigate(-1)}>
                              Back to Home
                            </Button>
                          </View>
                        </View>
                      )} */}

                {/* Appointment Status Card */}
                <Card className="border-none">
                  {/* <CardHeader className="pb-2">
                          <View className="flex justify-between items-center">
                            <Text
                              as="span"
                              className={`px-3 py-1 rounded-full text-xs   font-medium`}
                              style={getStatusColorScheme(data?.status)}
                            >
                              {data?.status}
                            </Text>
                          </View>
                        </CardHeader> */}
                  <CardContent>
                    {/* <View className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <View className="flex items-center text-sm">
                              <Calendar className="h-5 w-5 text-primary mr-2" />
                              <Text as="span">
                                {data?.appointment_date || "N/A"}
                              </Text>
                            </View>
                            <View className="flex items-center text-sm">
                              <Clock className="h-5 w-5 text-primary mr-2" />
                              <Text as="span">
                                {dayjs(
                                  dayjs().format("YYYY-MM-DD") +
                                    " " +
                                    data?.appointment_time,
                                  "YYYY-MM-DD HH:mm:ss"
                                ).format("hh:mm A")}
                              </Text>
                            </View>
                            
                          </View> */}

                    <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <View className="">
                        <View>
                          <Text as="h3" className="text-md font-semibold mb-2">
                            Appointment Date & Time
                          </Text>
                        </View>
                        <View className="flex items-center gap-4">
                          <View className="flex !items-center">
                            <Calendar className="h-5 w-5 text-primary mr-2" />
                            <Text as="span">
                              {data?.appointment_date || "N/A"}
                            </Text>
                            {/* <span>{formatDate(appointment.date)}</span> */}
                          </View>
                          <View className="flex !items-center">
                            <Clock className="h-5 w-5 text-primary mr-2" />
                            <Text as="span">
                              {dayjs(
                                dayjs().format("YYYY-MM-DD") +
                                  " " +
                                  data?.appointment_time,
                                "YYYY-MM-DD HH:mm:ss"
                              ).format("hh:mm A")}
                            </Text>
                          </View>
                        </View>
                        {/* <View className="flex items-center">
                            <MapPin className="h-5 w-5 text-primary mr-2" />
                            <span>{appointment.location}</span>
                          </View> */}
                      </View>

                      <View>
                        <Text as="h3" className="text-md font-semibold mb-2">
                          Appointment Type
                        </Text>
                        <Text as="p" className="text-muted-foreground ">
                          {data?.type || "N/A"}
                        </Text>
                      </View>
                      <View>
                        <Text as="h3" className="text-md font-semibold mb-2">
                          Enrollment Fees
                        </Text>
                        <Text as="p" className="text-muted-foreground ">
                          {patient?.enroll_fees ? currentSymbol : ""}
                          {patient?.enroll_fees || "N/A"}
                        </Text>
                      </View>
                    </View>

                    {data?.complaint && (
                      <View className="mt-4 p-4 bg-neutral-100 border border-border rounded-md dark:bg-background">
                        <Text as="h3" className="text-md font-semibold mb-2">
                          Complaints
                        </Text>
                        <Text as="p" className="text-sm">
                          {data?.complaint || "N/A"}
                        </Text>
                      </View>
                    )}
                  </CardContent>
                </Card>

                {/* Provider Information */}

                {/* <Card className={!readOnly ? "" : "border-none"}>
                        <CardHeader>
                          <CardTitle className="text-lg">Doctor Summary</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <View className="flex items-center mb-4">
                            <View className="w-16 h-16 rounded-full bg-accent-50 flex items-center justify-center text-accent text-xl font-bold mr-4">
                              {appointmentDetails?.doctor_name?.split(" ")[0][0]}
                            </View>
                            <View>
                              <Text as="h3" className="font-semibold text-xl">
                                <Link
                                  className="text-accent hover:underline"
                                  to={
                                    USER_TABLE_URL +
                                    USER_DETAIL_URL +
                                    "/" +
                                    appointmentDetails?.doctor_id
                                  }
                                >
                                  {appointmentDetails?.doctor_name}
                                </Link>
                              </Text>
                            </View>
                          </View>
              
                          <View className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <View className="flex items-center">
                              <Phone className="h-5 w-5 text-muted-foreground mr-2" />
                              <Text as="span">
                                {appointmentDetails?.doctor_phone || "N/A"}
                              </Text>
                            </View>
                            <View className="flex items-center">
                              <Mail className="h-5 w-5 text-muted-foreground mr-2" />
                              <Text as="span">
                                {appointmentDetails?.doctor_email || "N/A"}
                              </Text>
                            </View>
                          </View>
                        </CardContent>
                      </Card> */}
              </View>
            </Card>
          </View>
        );
      })}
    </React.Fragment>
  );
};

export default PatientDetailsPage;
