import { useState } from "react";
import { DayPicker } from "react-day-picker";
import "react-day-picker/dist/style.css";

type DateRange = {
  startDate: Date | null;
  endDate: Date | null;
};

interface DateRangePickerProps {
  onDateChange?: (range: DateRange) => void;
  initialStartDate?: Date | null;
  initialEndDate?: Date | null;
  className?: string;
  placeholder?: string;
}
const DateRangePicker: React.FC<DateRangePickerProps> = ({
  onDateChange,
  initialStartDate = null,
  initialEndDate = null,
  className = "",
  placeholder = "Select date range",
}) => {
  const [startDate, setStartDate] = useState(initialStartDate);
  const [endDate, setEndDate] = useState(initialEndDate);
  const [isOpen, setIsOpen] = useState(false);

  const handleDayClick = (day: any) => {
    if (!startDate || (startDate && endDate)) {
      // First click or reset selection
      setStartDate(day);
      setEndDate(null);
      if (onDateChange) {
        onDateChange({ startDate: day, endDate: null });
      }
    } else if (startDate && !endDate) {
      // Second click - set end date
      if (day < startDate) {
        // If clicked date is before start date, swap them
        setStartDate(day);
        setEndDate(startDate);
        if (onDateChange) {
          onDateChange({ startDate: day, endDate: startDate });
        }
      } else {
        setEndDate(day);
        if (onDateChange) {
          onDateChange({ startDate, endDate: day });
        }
      }
      setIsOpen(false);
    }
  };

  const formatDateRange = () => {
    if (!startDate) return placeholder;
    if (!endDate) return `${startDate.toLocaleDateString()} - Select end date`;
    return `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`;
  };

  const clearSelection = () => {
    setStartDate(null);
    setEndDate(null);
    if (onDateChange) {
      onDateChange({ startDate: null, endDate: null });
    }
  };

  const selectedRange =
    startDate && endDate
      ? {
          from: startDate,
          to: endDate,
        }
      : undefined;

  return (
    <div className={`relative inline-block ${className}`}>
      {/* Input Display */}
      <div
        className="flex items-center justify-between px-4 py-2 border border-gray-300 rounded-lg cursor-pointer bg-white hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-64"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className={startDate ? "text-gray-900" : "text-gray-500"}>
          {formatDateRange()}
        </span>
        <div className="flex items-center space-x-2">
          {startDate && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                clearSelection();
              }}
              className="text-gray-400 hover:text-gray-600 text-sm"
            >
              ✕
            </button>
          )}
          <span className="text-gray-400">📅</span>
        </div>
      </div>

      {/* Calendar Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-sm font-medium text-gray-700">
                Select Date Range
              </h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            {/* Instructions */}
            <div className="text-xs text-gray-500 mb-3">
              {!startDate && "Click a date to select start date"}
              {startDate && !endDate && "Click another date to select end date"}
              {startDate && endDate && "Click a date to select new range"}
            </div>

            <DayPicker
              mode="range"
              selected={selectedRange}
              onDayClick={handleDayClick}
              modifiers={{
                ...(startDate && { start: startDate }),
                ...(endDate && { end: endDate }),
                ...(selectedRange && { range: selectedRange }),
              }}
              modifiersStyles={{
                start: {
                  backgroundColor: "#3b82f6",
                  color: "white",
                  borderRadius: "6px 0 0 6px",
                },
                end: {
                  backgroundColor: "#3b82f6",
                  color: "white",
                  borderRadius: "0 6px 6px 0",
                },
                range: {
                  backgroundColor: "#dbeafe",
                  color: "#1e40af",
                },
              }}
              styles={{
                day: {
                  margin: "2px",
                  borderRadius: "6px",
                  cursor: "pointer",
                },
                day_selected: {
                  backgroundColor: "#3b82f6",
                  color: "white",
                },
              }}
            />

            {/* Action Buttons */}
            <div className="flex justify-between mt-4 pt-3 border-t border-gray-200">
              <button
                onClick={clearSelection}
                className="text-sm text-gray-500 hover:text-gray-700"
                disabled={!startDate}
              >
                Clear
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="px-4 py-2 bg-blue-500 text-white text-sm rounded-lg hover:bg-blue-600 transition-colors"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DateRangePicker;
