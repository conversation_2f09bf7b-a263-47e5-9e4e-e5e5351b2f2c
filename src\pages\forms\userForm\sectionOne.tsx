import View from "@/components/view";
import Input from "@/components/input";
import Select from "@/components/Select";
import { useSelector } from "react-redux";
import React, { useEffect } from "react";
import { UserInterface } from "@/interfaces/users";
import useForm from "@/utils/custom-hooks/use-form";
import { Gender, Ids, MaratalStatus } from "@/interfaces";
import { useAgeCalculate } from "@/utils/custom-hooks/use-age-calculate";
import Text from "@/components/text";
import Upload from "@/components/Upload";
import SingleSelector from "@/components/SingleSelector";
import TransferList from "@/components/TransferList";
// import MultiSelector from "@/components/MultiSelector2";
// import TransferList from "@/components/TransferList";

interface SectionOneProps {
  errorsName: string;
  errorsEmail: string;
  errorsPhone: string;
  errorsDOB: string;
  errorsGender: string;
  errorsMaritalStatus: string;
  errorsIds: string;
  errorsIdValue: string;
  errorConsent: string;
  errorsImage: string;
  errorsAge: string;
  formType: "add" | "edit";
  setImage: (name: string, value: any) => void;
}

const SectionOne: React.FC<SectionOneProps> = ({
  errorsDOB,
  errorsName,
  errorsEmail,
  errorsPhone,
  errorsGender,
  errorsAge,
  errorsMaritalStatus,
  errorsIdValue,
  errorsIds,
  errorConsent,
  errorsImage,
  setImage,
  formType,
}) => {
  const { userAge, calculateAge } = useAgeCalculate();
  const userDetails = useSelector((state: any) => state?.users?.userDetails);
  
  const userDetailsData = {...userDetails, id_value: userDetails?.id_number_masked, id_edited: false};
  const [test, setTest] = React.useState<any[]>([]);

  const { values, handleChange, onSetHandler } =
    useForm<UserInterface>(userDetailsData);

  const maritalStatusData = [
    { id: MaratalStatus.SINGLE, name: "Single jkslfjlaskdlsjfkkdsfjsfjsfkdsljfdsfjsfdsldskdlsdkkfssdljlkfdjfklsfljsfksflsjflskfksjfsfsfjlsfskfjsflksfsfjslkfsjfksfdjkdsflksdls", description: "Single jsldklkdjlsjlsdksdk" },
    { id: MaratalStatus.MARRIED, name: "Married" },
    { id: MaratalStatus.DIVORCED, name: "Divorced" },
    { id: "1", name: "Widowed" },
  ];

  useEffect(() => {
    onSetHandler("gender", userDetails?.gender);
    onSetHandler("marital_status", userDetails?.marital_status);
    if (userDetails?.DOB) {
      onSetHandler("DOB", userDetails?.DOB);
      calculateAge(userDetails?.DOB);
    }
    if (values?.id_number_masked) {
      onSetHandler("id_value", values?.id_number_masked);
    }
  }, [userDetails?.DOB, userDetails?.gender, userDetails?.marital_status]);  

  console.log("values", values);
  
  // const Examples: any = {
  //   Aadhar: "123456789012",
  //   Passport: "********",
  //   Voter_ID: "123456789012",
  //   PAN: "**********",
  //   Driving_License: "123456789012",
  //   Ration_Card: "123456789012",
  // };

  return (
    <React.Fragment>
      <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* First Name */}
        <View>
          <Input
            name="name"
            type="text"
            label="First Name"
            id="firstName"
            placeholder="Ex: Vishnu Kumar"
            error={errorsName}
            value={values?.name}
            onChange={handleChange}
            required={true}
          />
        </View>

        {/* Email */}
        <View>
          <Input
            type="text"
            label="Email"
            id="email"
            name="email"
            error={errorsEmail}
            value={values?.email}
            onChange={handleChange}
            placeholder="Ex: <EMAIL>"
            required={true}
          />
        </View>

        {/* Phone */}
        <View>
          <Input
            id="phone"
            type="text"
            label="Phone"
            name="phone"
            error={errorsPhone}
            value={values?.phone}
            onChange={handleChange}
            placeholder="Ex: 8765432109"
            required={true}
          />
        </View>
        {/* Date of Birth */}
        <View>
          <Input
            id="dob"
            name="DOB"
            label="Date of Birth"
            type="date"
            error={errorsDOB}
            max={new Date().toISOString().split("T")[0]}
            value={
              values?.DOB instanceof Date
                ? values?.DOB?.toISOString().split("T")[0]
                : values?.DOB || ""
            }
            onBlur={(e) => {
             onSetHandler("DOB", e.currentTarget.value);
              if (values?.DOB) {
                calculateAge(new Date(values?.DOB).toISOString().split("T")[0]);
              }
              onSetHandler("age", userAge);
            }}
            onKeyUp={(e) => {
             onSetHandler("DOB", e.currentTarget.value);
              if (values?.DOB) {
                calculateAge(new Date(values?.DOB).toISOString().split("T")[0]);
              }
              onSetHandler("age", userAge);
            }}
            onChange={(e) => {
              onSetHandler("DOB", e.currentTarget.value);
              if (values?.DOB) {
                calculateAge(new Date(values?.DOB).toISOString().split("T")[0]);
              }
              onSetHandler("age", userAge);
            }}
          />
        </View>

        <View>
          <Input
            id="age"
            label="Age"
            placeholder="Enter Age"
            readOnly={(values?.DOB) ? true : false}
            name="age"
            value={values?.age ? values?.age + "" : !values?.DOB && userAge ? "" : userAge}
            onChange={handleChange}
            // required={true}
            error={errorsAge}
          />
        </View>

        {/* Gender */}
        <View>
          {/* <Select
            id="gender"
            label="Gender"
            name="gender"
            error={errorsGender}
            value={values?.gender}
            placeholder="Gender"
            onChange={(e) => {
              onSetHandler("gender", e.currentTarget.value);
            }}
            options={[
              { value: Gender.MALE, label: "Male" },
              { value: Gender.FEMALE, label: "Female" },
              { value: Gender.OTHER, label: "Other" },
            ]}
            required={true}
          /> */}
          <SingleSelector
            id="gender"
            label="Gender"
            name="gender"
            error={errorsGender}
            value={values?.gender || ""}
            placeholder="Select Gender"
            onChange={(value) => {
              onSetHandler("gender", value);
            }}
            options={[
              { value: Gender.MALE, label: "Male" },
              { value: Gender.FEMALE, label: "Female" },
              { value: Gender.OTHER, label: "Other" },
            ]}
            closeOnSelect={true}
            required={true}
            
          />
        </View>

        {/* Marital Status */}
        <View className="col-span-2">
          {/* <Select
            id="maratalStatus"
            label="Marital Status"
            name="marital_status"
            error={errorsMaritalStatus}
            value={values?.marital_status}
            placeholder="Marital Status"
            onChange={(e) => {
              onSetHandler("marital_status", e?.currentTarget?.value);
            }}
            options={[
              { value: MaratalStatus.SINGLE, label: "Single" },
              { value: MaratalStatus.MARRIED, label: "Married" },
              { value: MaratalStatus.DIVORCED, label: "Divorced" },
              // { value: MaratalStatus.WIDOWED, label: "Widowed" },
            ]}
            required={true}
          /> */}

          <TransferList
            // id="marital_status"
            label="Marital Status"
            // name="marital_status"
            error={errorsMaritalStatus}
            // value={values?.marital_status ? [values?.marital_status] : []}
            onSelectionChange={(value: any) => {
              setTest(value);
              
            }}
            // value={test || []}
            // onChange={(value) => {
            //   setTest(value);
            // }}
            sourceData={maritalStatusData}
            selectedItems={ test || []}
            required={true}
            allowCustomValues={true}
          />
          {/* <TransferList
  availableItems={yourItems}
  selectedItems={selectedItems}
  onChange={handleSelectionChange}
  title="Custom Title"
  availableTitle="Available Options"
  selectedTitle="Selected Options"
  searchPlaceholder="Search..."
  disabled={false}
  showCounter={true}
  maxHeight="400px"
  itemKey="id"
  itemLabel="name"
  itemDescription="description"
/> */}
          {/* <SingleSelector
            id="marital_status"
            label="Marital Status"
            name="marital_status"
            error={errorsMaritalStatus}
            value={values?.marital_status || ""}
            placeholder="Marital Status"
            onChange={(value) => {
              onSetHandler("marital_status", value);
            }}
            options={[
              { value: MaratalStatus.SINGLE, label: "Single" },
              { value: MaratalStatus.MARRIED, label: "Married" },
              { value: MaratalStatus.DIVORCED, label: "Divorced" },
              // { value: MaratalStatus.WIDOWED, label: "Widowed" },
            ]}
            closeOnSelect={true}
            required={true}
          /> */}
        </View>
        {/* {userDetails?.id_type && userDetails?.id_number_masked ? (
          <View className="flex items-center space-y-4">
            <Text className="text-base font-medium">
              {userDetails?.id_type} : {userDetails?.id_number_masked}
            </Text>
          </View>
        ) : (
          <> */}
            <View className="space-y-4">
              <Select
                id="id_type"
                label="Identifications"
                name="id_type"
                error={errorsIds}
                value={values?.id_type || ""}
                placeholder="Select ID"
                onChange={(e) => {
                  onSetHandler("id_type", e?.currentTarget?.value);
                }}
                options={[
                  { value: Ids.ADHAR, label: "Aadhar" },
                  { value: Ids.PASSPORT, label: "Passport" },
                  { value: Ids.VOTER_ID, label: "Voter ID" },
                  { value: Ids.DRIVING_LICENSE, label: "Driving License" },
                  { value: Ids.RATION_CARD, label: "Ration Card" },
                  { value: "", label: "None" },
                  // { value: MaratalStatus.WIDOWED, label: "Widowed" },
                ]}
              />
              {/* <SingleSelector
                id="id_type"
                label="Identifications"
                name="id_type"
                error={errorsIds}
                value={values?.id_type || ""}
                placeholder="Select ID"
                onChange={(value) => {
                  onSetHandler("id_type", value);
                }}
                options={[
                  { value: Ids.ADHAR, label: "Aadhar" },
                  { value: Ids.PASSPORT, label: "Passport" },
                  { value: Ids.VOTER_ID, label: "Voter ID" },
                  { value: Ids.DRIVING_LICENSE, label: "Driving License" },
                  { value: Ids.RATION_CARD, label: "Ration Card" },
                  { value: "", label: "None" },
                ]}
                required={true}
              /> */}
              {values?.id_type ? (
                <>
                  
                  <Input
                  name="id_value"
                  label={`Enter ${values?.id_type?.charAt(0)?.toUpperCase() +
                    values?.id_type?.slice(1)} Number`}
                  onChange={(e) => {
                    if (formType === "edit") {
                      setImage("id_edited", true);
                    }else {
                      setImage("id_edited", false);
                    }
                    onSetHandler("id_value", e.target.value);
                  }}
                  error={errorsIdValue}
                  // value={formType === "edit" ? values?.id_value ? values?.id_value || "" : values?.id_number_masked || "" : values?.id_value || ""}
                  value={values?.id_value || ""}
                  placeholder="Enter ID Number"
                  required={true}
                />

                <View className="flex items-center justify-center space-x-2">
                <View>
                  <Input
                    type="checkbox"
                    name="consent"
                    checked={!!values?.consent}
                    onChange={(e) => {
                      onSetHandler("consent", e.target.checked);
                    }}
                    value={values?.consent}
                  />
                </View>
                <Text className="font-sm w-full">
                  I consent to the storage of my ID for address verification.
                </Text>
              </View>
              {errorConsent ? (
                <Text className="text-sm text-red-500">{errorConsent}</Text>
              ) : null}
                </>
              ) : null}
              
            </View>
            {
              values?.id_type ? (
                <View className="col-span-2">
              <Upload 
                label="Upload ID Proof (Adhar Card, Passport, Voter ID, etc.)"
                name="image"
                accept=".pdf,.doc,.docx,.txt,.jpg,.png"
                maxSize={1024 * 1024 * 2}
                multiple = {false}
                maxCount={1}
                // required={true}
                error={errorsImage}
                existingFiles={
                  typeof values?.image === "string"
                    ? values?.image
                    : Array.isArray(values?.image) &&
                      values?.image.length > 0
                    ? values?.image
                        .filter((item) => typeof item === "string")
                        .join(",")
                    : ""
                }
                onChange={(fileList: any) => {
                  // Extract the actual File objects from FileItem array
                  
                  const file = fileList?.map((item: any) => {
                    if(item.isExisting){
                      return item.url;
                    }else{
                      return item.file;
                    }
                  }) || [];
  
                  setImage("image", file);
                }}
              />
            </View>
              ) : null

            }
          {/* </>
        )} */}
      </View>
    </React.Fragment>
  );
};

export default SectionOne;
