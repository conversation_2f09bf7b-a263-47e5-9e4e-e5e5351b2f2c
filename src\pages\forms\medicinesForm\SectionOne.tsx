import Input from "@/components/input";
import Select from "@/components/Select";
import View from "@/components/view";
import useForm from "@/utils/custom-hooks/use-form";
// import SearchSelect from "@/components/SearchSelect";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { Medicine } from "@/interfaces/medicines";
import SingleSelector from "@/components/SingleSelector";

interface SectionOneProps {
  // errorsPatientId: string;
  // errorsDoctorId: string;
  // errorsAppointmentId: string;
}
const SectionOne: React.FC<SectionOneProps> = (
  {
    // errorsPatientId,
    // errorsAppointmentId,
    // errorsDoctorId,
  }
) => {
  const medicineDetails = useSelector(
    (state: RootState) => state.medicines.medicineDetailData
  );

  const { values, handleChange, onSetHandler } = useForm<Medicine | null>(medicineDetails);
  return (
    <View className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* <View>
        <Select
          id="type"
          name="type"
          required={true}
          error={errorsType}
          label="Appointment Type"
          placeholder="Select Appointment Type"
          options={appointmentTypeOptions}
          value={values?.type}
          onChange={(e) => onSetHandler("type", e.target.value)}
        />
      </View> */}
      <View>
        <Input
          id="medicine_name"
          name="medicine_name"
          label="Medicine Name"
          placeholder="Ex: Paracetamol"
          onChange={handleChange}
          value={values?.medicine_name}
          required={true}
        />
      </View>
      <View>
        <Input
          id="generic_name"
          name="generic_name"
          label="Generic Name"
          placeholder="Ex: Acetaminophen"
          onChange={handleChange}
          value={values?.generic_name}
        />
      </View>
      <View>
        <Input
          id="manufacturer"
          name="manufacturer"
          label="Manufacturer Name"
          placeholder="Ex: Johnson & Johnson"
          onChange={handleChange}
          value={values?.manufacturer}
          required={true}
        />
      </View>
      <View>
        {/* <Select
          id="is_active"
          name="is_active"
          required={true}
          label="Is Active"
          placeholder="Select Is Active"
          options={[
            { label: "Yes", value: "1" },
            { label: "No", value: "0" },
          ]}
          value={values?.is_active + ""}
          onChange={handleChange}
          // error={errorsPatientId}
        /> */}
        <SingleSelector
          id="is_active"
          label="Is Active"
          name="is_active"
          // error={errorsIsActive}
          value={values?.is_active === null || values?.is_active === undefined ? "1" : values?.is_active ? "1" : "0" }
          placeholder="Select Is Active"
          onChange={(value) => {
            onSetHandler("is_active", value);
          }}
          options={[
            { label: "Yes", value: "1" },
            { label: "No", value: "0" },
          ]}
          required={true}
        />
      </View>
    </View>
  );
};
export default SectionOne;
