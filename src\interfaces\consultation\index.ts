import { GenericStatus } from "..";

export type paymentStatus = GenericStatus.PENDING | GenericStatus.COMPLETED;
// export type AppointmentTypeProps = "Follow-up" | "First Visit";
export enum AppointmentTypeProps {
  "Follow-up" = "Follow-up",
  "First Visit" = "First Visit",
}
export type ExaminationType = "Proctology" | "Non Proctology";

export interface Consultation {
  id?: number | string;
  appointment_id: string;
  patient_id: string;
  doctor_id: number;
  next_visit_date?: Date;
  referred_by_name?: string;
  complaint: string;
  advice: string;
  preliminary_diagnosis: string;
  preliminary_diagnostic: string;
  diagnosis_summary: string;
  description: string;
  temperature: string;
  temperature_unit: string;
  bp: string;
  pulse: string;
  cvs: string;
  rs: string;
  type: ExaminationType;
  appointment_type: AppointmentTypeProps;

  complaint_name?: any;
  surgical_history?: Array<{ id: number | string; label: string | number; value: string | number }>[];
  co_morbidities?: Array<{ id: number | string; label: string | number; value: string | number }>[];
  co_morbidities_data?: Array<{name: string; description: string; is_chronic: boolean; consultation_id: number | string, comorbidities_id: number | string  }>[];
  on_examination?: Array<{ id: number | string; label: string | number; value: string | number }>[];
  diat_plan?: Array<{ id: number | string; label: string | number; value: string | number }>[];
  diagnosis?: Array<{ id: number | string; label: string | number; value: string | number }>[];
  plan?: string;
  amount?: number;
  currency?: string;

  // description: string;
  examination_overview?: string;
  medicines?: string;
  finding_fields: string[];

  dosage?: string;
  timing?: string;

  // Proctology
   doc_upload: File[] | string[] | null;
   advice_field: string,
   consultation_id: number,  
   advice_admition?: boolean;
   test_id?: any;
  //  test_id?: number;
   fees?: number;

  // findings?: string[];

  // Non-Proctology
  food_prescription?: string;
  yoga_prescription?: string;
  vikruti?: string;
  prakriti?: string;
  koshta?: string;
  avastha?: string;
  agni?: string;

  breakfast?: string;
  lunch?: string;
  dinner?: string;
  general_advice?: string;
  yoga_asana?: string;

  payment_status: paymentStatus;
  status:
    | GenericStatus.PENDING
    | GenericStatus.COMPLETED
    | GenericStatus.REJECTED
    | GenericStatus.CANCELLED
    | GenericStatus.ONGOING
    | GenericStatus.CLOSED
    | GenericStatus.RESCHEDULED;

    existing_documents?: string[];
    new_documents?: File[];
}
