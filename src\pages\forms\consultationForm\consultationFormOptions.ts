import { GenericStatus } from "@/interfaces";
import { AppointmentTypeProps } from "@/interfaces/consultation/index";

export const paymentStatusOptions = [
  GenericStatus.PENDING,
  GenericStatus.COMPLETED,
].map((value) => ({
  value,
  label: value.replace(/_/g, " "),
}));

export const statusOptions = [
  GenericStatus.PENDING,
  GenericStatus.COMPLETED,
  GenericStatus.REJECTED,
  GenericStatus.CANCELLED,
  GenericStatus.ONGOING,
  GenericStatus.CLOSED,
  GenericStatus.RESCHEDULED,
].map((value) => ({
  value,
  label: value.replace(/_/g, " "),
}));

export const appointmentTypeOptions = [
  AppointmentTypeProps["First Visit"],
  AppointmentTypeProps["Follow-up"],
].map((value) => ({
  value,
  label: value.replace(/_/g, " "),
}));

// export const findingOptions = findingDropdownData?.map((value) => ({
//   value,
//   label: value.replace(/_/g, " "),
// }));
