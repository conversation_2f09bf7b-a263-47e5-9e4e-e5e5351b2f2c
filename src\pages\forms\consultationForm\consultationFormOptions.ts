import { GenericStatus } from "@/interfaces";

export const paymentStatusOptions = [
  GenericStatus.PENDING,
  GenericStatus.COMPLETED,
].map((value) => ({
  value,
  label: value.replace(/_/g, " "),
}));

export const statusOptions = [
  GenericStatus.PENDING,
  GenericStatus.COMPLETED,
  GenericStatus.REJECTED,
  GenericStatus.CANCELLED,
  GenericStatus.ONGOING,
  GenericStatus.CLOSED,
  GenericStatus.RESCHEDULED,
].map((value) => ({
  value,
  label: value.replace(/_/g, " "),
}));

// export const findingOptions = findingDropdownData?.map((value) => ({
//   value,
//   label: value.replace(/_/g, " "),
// }));
