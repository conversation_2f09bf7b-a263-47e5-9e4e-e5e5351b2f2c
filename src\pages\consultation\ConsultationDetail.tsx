import { useEffect } from "react";
import { <PERSON>, useParams } from "react-router-dom";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import Button from "@/components/button";
import InfoCard from "@/components/ui/infoCard";
import {
  Calendar,
  User,
  Thermometer,
  HeartPulse,
  Stethoscope,
  Pill,
  Mail,
  Phone,
  Download,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useDispatch, useSelector } from "react-redux";
import { useConsultation } from "@/actions/calls/consultation";
import { RootState } from "@/actions/store";
import { clearConsultationDetailSlice } from "@/actions/slices/consultation";
import getStatusColorScheme from "@/utils/statusColorSchemaDecider";
import dayjs from "dayjs";
import {
  APPOINTMENT_DETAILS_URL,
  APPOINTMENT_TABLE_URL,
  MEDICINE_DETAILS_URL,
  MEDICINE_TABLE_URL,
} from "@/utils/urls/frontend";
import View from "@/components/view";
import Text from "@/components/text";
import { useNavigate } from "react-router-dom";
import { toast } from "@/utils/custom-hooks/use-toast";
import { useInvoice } from "@/actions/calls/invoice";

const ConsultationDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { downloadConsultationHandler } = useInvoice();
  const { consultationDetailHandler, cleanUp } = useConsultation();

  const consultationData = useSelector(
    (state: RootState) => state.consultation.consultationDetailData
  );

  

  

  useEffect(() => {
    if (id) {
      consultationDetailHandler(id, () => {});
    }
    return () => {
      cleanUp();
      dispatch(clearConsultationDetailSlice());
    };
  }, [id]);
  const handleDownloadConsultation = () => {
    if (id) {
      downloadConsultationHandler(id, async (success: boolean) => {
        if (success) {
          toast({
            title: "Success!",
            description: "Successfully downloaded consultation",
            variant: "success",
          });
        } else {
          toast({
            title: "Error",
            description: "Failed to download consultation",
            variant: "destructive",
          });
        }
      });
    }
  };

  return (
    <View className="space-y-6 container mx-auto py-8">
      <View className="flex justify-end">
        <Button
          onClick={handleDownloadConsultation}
          className="flex items-center gap-2"
        >
          <Download size={16} />
          Download Consultation
        </Button>
      </View>
      <View className="flex justify-between items-center">
        <View>
          <View className="flex items-center mb-2">
            <Text as="h1" weight="font-semibold" className="text-2xl font-bold">
              Consultation Details
            </Text>
            <Badge
              className={`ml-4`}
              style={getStatusColorScheme(
                consultationData?.consultations?.status
              )}
            >
              {consultationData?.consultations?.status || "N/A"}
            </Badge>
          </View>
          <Text as="p" className="text-muted-foreground">
            Medical consultation information for{" "}
            {consultationData?.consultations?.patient_name || "N/A"}
          </Text>
        </View>
        <View className="flex space-x-2">
          <Link
            to={`${APPOINTMENT_TABLE_URL}${APPOINTMENT_DETAILS_URL}/${consultationData?.consultations?.appointment_id}`}
          >
            <Button variant="outline">View Appointment</Button>
          </Link>
          <Button onClick={() => navigate(-1)} className="flex gap-2">
            Back to Home
          </Button>
        </View>
      </View>

      {/* Basic Consultation Information */}
      <Card>
        <CardHeader>
          <CardTitle>Consultation Information</CardTitle>
        </CardHeader>
        <CardContent>
          <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <InfoCard
              label="Appointment Number"
              value={
                consultationData?.consultations?.appointment_number || "N/A"
              }
              icon={<Calendar className="h-5 w-5 text-primary" />}
            />
            <InfoCard
              label="Patient Number"
              value={consultationData?.consultations?.patient_number || "N/A"}
              icon={<User className="h-5 w-5 text-primary" />}
            />
            {/* <InfoCard
              label="Complaint"
              value={consultationData?.consultations?.complaint || "N/A"}
              icon={<MessageSquare className="h-5 w-5 text-primary" />}
            /> */}
            <InfoCard
              label="Next Visit"
              value={
                dayjs(consultationData?.consultations?.next_visit_date)?.format(
                  "MMM D, YYYY"
                ) || "N/A"
              }
              icon={<Calendar className="h-5 w-5 text-primary" />}
            />
          </View>

          <View className="mt-6">
            <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Patient Information */}
              <View className="space-y-2">
                <Text as="h3" className="font-semibold">
                  Patient Information
                </Text>
                <View className="flex items-start space-x-4">
                  <View className="w-12 h-12 rounded-full bg-primary-50 flex items-center justify-center text-primary text-lg font-bold">
                    {consultationData?.consultations?.patient_name.charAt(0)}
                  </View>
                  <View>
                    <Text as="h3" className="font-medium text-lg">
                      {consultationData?.consultations?.patient_name || "N/A"}
                    </Text>
                    <View className="text-sm text-muted-foreground mt-1">
                      <a
                        href={`mailto:${consultationData?.consultations?.patient_email}`}
                      >
                        <View className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          {consultationData?.consultations?.patient_email ||
                            "N/A"}
                        </View>
                      </a>
                      <a
                        href={`tel:${consultationData?.consultations?.patient_phone}`}
                      >
                        <View className="flex items-center gap-2 mt-1">
                          <Phone className="h-4 w-4" />
                          {consultationData?.consultations?.patient_phone ||
                            "N/A"}
                        </View>
                      </a>
                      <View className="flex items-center gap-2 mt-1">
                        <User className="h-4 w-4" />
                        {consultationData?.consultations?.patient_number ||
                          "N/A"}
                      </View>
                    </View>
                  </View>
                </View>
              </View>

              {/* Doctor Information */}
              <View className="space-y-2">
                <Text as="h3" className="font-semibold">
                  Doctor Information
                </Text>
                <View className="flex items-start space-x-4">
                  <View className="w-12 h-12 rounded-full bg-secondary-50 flex items-center justify-center text-secondary text-lg font-bold">
                    {consultationData?.consultations?.doctor_name?.charAt(0)}
                  </View>
                  <View>
                    <Text as="h3" className="font-medium text-lg">
                      Dr.{" "}
                      {consultationData?.consultations?.doctor_name || "N/A"}
                    </Text>
                    <View className="text-sm text-muted-foreground mt-1">
                      <a
                        href={`mailto:${consultationData?.consultations?.doctor_email}`}
                      >
                        <View className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          {consultationData?.consultations?.doctor_email ||
                            "N/A"}
                        </View>
                      </a>
                      <a
                        href={`tel:${consultationData?.consultations?.doctor_phone}`}
                      >
                        <View className="flex items-center gap-2 mt-1">
                          <Phone className="h-4 w-4" />
                          {consultationData?.consultations?.doctor_phone ||
                            "N/A"}
                        </View>
                      </a>
                    </View>
                  </View>
                </View>
              </View>

              {/* Front Desk User Information */}
              <View className="space-y-2">
                <Text as="h3" className="font-semibold">
                  Front Desk Information
                </Text>
                <View className="flex items-start space-x-4">
                  <View className="w-12 h-12 rounded-full bg-accent-50 flex items-center justify-center text-accent text-lg font-bold">
                    {consultationData?.consultations?.front_desk_user_name?.charAt(
                      0
                    )}
                  </View>
                  <View>
                    <Text as="h3" className="font-medium text-lg">
                      {consultationData?.consultations?.front_desk_user_name ||
                        "N/A"}
                    </Text>
                    <View className="text-sm text-muted-foreground mt-1">
                      <a
                        href={`mailto:${consultationData?.consultations?.front_desk_user_email}`}
                      >
                        <View className="flex items-center gap-2">
                          <Mail className="h-4 w-4" />
                          {consultationData?.consultations
                            ?.front_desk_user_email || "N/A"}
                        </View>
                      </a>
                      <a
                        href={`tel:${consultationData?.consultations?.front_desk_user_phone}`}
                      >
                        <View className="flex items-center gap-2 mt-1">
                          <Phone className="h-4 w-4" />
                          {consultationData?.consultations
                            ?.front_desk_user_phone || "N/A"}
                        </View>
                      </a>
                    </View>
                  </View>
                </View>
              </View>
              <View className="space-y-2">
                <Text as="h3" className="font-semibold">
                  Complaint
                </Text>
                <Text as="p" className="text-muted-foreground">
                  {consultationData?.consultations?.complaint || "N/A"}
                </Text>
              </View>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Vitals & Examination */}
      <Card>
        <CardHeader>
          <CardTitle>Vitals & Examination</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <View className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <InfoCard
              label="Temperature"
              value={consultationData?.vitals?.temperature || "N/A"}
              icon={<Thermometer className="h-5 w-5 text-red-500" />}
              iconStyle="bg-red-100"
            />
            <InfoCard
              label="Blood Pressure"
              value={`${consultationData?.vitals?.bp || "N/A"} mmHg`}
              icon={<HeartPulse className="h-5 w-5 text-red-500" />}
              iconStyle="bg-red-100"
            />
            <InfoCard
              label="Pulse"
              value={`${consultationData?.vitals?.pulse || "N/A"} bpm`}
              icon={<HeartPulse className="h-5 w-5 text-red-500" />}
              iconStyle="bg-red-100"
            />
          </View>

          <Separator className="my-4" />

          <View>
            <Text as="h3" className="text-lg font-semibold mb-2">
              System Examination
            </Text>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>System</TableHead>
                  <TableHead>Findings</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium flex items-center">
                    <Stethoscope className="h-4 w-4 mr-2" /> CVS
                  </TableCell>
                  <TableCell>
                    <View
                      dangerouslySetInnerHTML={{
                        __html: consultationData?.vitals?.cvs || "N/A",
                      }}
                    >
                      {null}
                    </View>
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium flex items-center">
                    <Stethoscope className="h-4 w-4 mr-2" /> RS
                  </TableCell>
                  <TableCell>
                    <View
                      dangerouslySetInnerHTML={{
                        __html: consultationData?.vitals?.rs || "N/A",
                      }}
                    >
                      {null}
                    </View>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </View>

          <View className="space-y-3">
            {/* <View>
              <Text as="h3" className="text-lg font-semibold mb-1">
                Examination Description
              </Text>
              <View className="text-sm border rounded-md p-3 bg-neutral-100 dark:bg-background dark:border-border">
                <View
                  dangerouslySetInnerHTML={{
                    __html:
                      consultationData?.examinations?.description || "N/A",
                  }}
                >
                  {null}
                </View>
              </View>
            </View> */}

            <View>
              <Text as="h3" className="text-lg font-semibold mb-1">
                Examination Overview
              </Text>
              <View className="text-sm border rounded-md p-3 bg-neutral-100 dark:bg-background dark:border-border">
                <View
                  dangerouslySetInnerHTML={{
                    __html:
                      consultationData?.examinations?.examination_overview ||
                      "N/A",
                  }}
                >
                  {null}
                </View>
              </View>
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Diagnosis & Advice */}
      <Card>
        <CardHeader>
          <CardTitle>Diagnosis & Advice</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <View>
            <Text as="h3" className="text-lg font-semibold mb-1">
              Preliminary Diagnosis
            </Text>
            <View className="text-sm border rounded-md p-3 bg-neutral-100 dark:bg-background dark:border-border">
              {consultationData?.consultations?.preliminary_diagnosis || "N/A"}
            </View>
          </View>

          <View>
            <Text as="h3" className="text-lg font-semibold mb-1">
              Advice
            </Text>
            <View className="text-sm border rounded-md p-3 bg-neutral-100 dark:bg-background dark:border-border">
              {consultationData?.consultations?.advice || "N/A"}
            </View>
          </View>
        </CardContent>
      </Card>

      {/* Medications */}
      <Card>
        <CardHeader>
          <CardTitle>Prescribed Medicines</CardTitle>
        </CardHeader>
        <CardContent>
          <View className="space-y-4">
            {consultationData?.proctologyOrNonProctology?.medicines ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Medicine</TableHead>
                    <TableHead>Dosage</TableHead>
                    <TableHead>Timing</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {
                    consultationData?.proctologyOrNonProctology?.medicines?.split(",")?.map((medicine: any) => {
    const [medicineName, _, dosage, timing] = medicine?.split("#");
            return (
                 <TableRow key={medicine.id}>
                      <TableCell className="font-medium flex items-center">
                        <Pill className="h-4 w-4 mr-2" />{" "}
                        {/* <Link
                          className="text-primary hover:underline"
                          to={`${MEDICINE_TABLE_URL}${MEDICINE_DETAILS_URL}/${medicineName}`}
                        >
                          {medicineName || "N/A"}
                        </Link> */}
                        {medicineName || "N/A"}
                      </TableCell>
                      <TableCell>{dosage || "N/A"}</TableCell>
                      <TableCell>{`${timing || "N/A"}`}</TableCell>
                    </TableRow>
            )
  })
                  }
                  
                </TableBody>
              </Table>
            ) : (
              <View className="text-center py-4 text-muted-foreground">
                No medicines prescribed
              </View>
            )}
            {/* {consultationData?.medicines?.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Medicine</TableHead>
                    <TableHead>Dosage Form</TableHead>
                    <TableHead>Strength</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {consultationData?.medicines?.map((medicine: any) => (
                    <TableRow key={medicine.id}>
                      <TableCell className="font-medium flex items-center">
                        <Pill className="h-4 w-4 mr-2" />{" "}
                        <Link
                          className="text-primary hover:underline"
                          to={`${MEDICINE_TABLE_URL}${MEDICINE_DETAILS_URL}/${medicine.id}`}
                        >
                          {medicine.medicine_name || "N/A"}
                        </Link>
                      </TableCell>
                      <TableCell>{medicine.dosage_form}</TableCell>
                      <TableCell>{`${medicine.strength} ${medicine.strength_unit}`}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <View className="text-center py-4 text-muted-foreground">
                No medicines prescribed
              </View>
            )} */}
          </View>
        </CardContent>
      </Card>
    </View>
  );
};

export default ConsultationDetails;
