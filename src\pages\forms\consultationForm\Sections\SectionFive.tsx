import React, { useEffect } from "react";
import Input from "@/components/input";
import Select from "@/components/Select";
import View from "@/components/view";
// import { Appointment } from "@/interfaces/appointments";
import useForm from "@/utils/custom-hooks/use-form";
// import Textarea from "@/components/Textarea";
// import SearchSelect from "@/components/SearchSelect";
// import { useOpd } from "@/actions/calls/opd";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
// import { useEffect } from "react";
// import dayjs from "dayjs";
import TipTapTextEditor from "@/components/TipTapTexteditor";
import { Consultation } from "@/interfaces/consultation";
import { useTest } from "@/actions/calls/test";
import SearchSelect from "@/components/SearchSelect";
import MultiSelectWithDropDown from "@/components/MultiSelectWithDropDown";
import MultiSelector from "@/components/MultiSelector";
import SingleSelector from "@/components/SingleSelector";
import TransferList from "@/components/TransferList";
import { Card } from "@/components/ui/card";
import Text from "@/components/text";
import { useMedicine } from "@/actions/calls/medicine";
import MedicinesSection from "@/components/MedicinesSection";
import DynamicFormSection from "@/components/DynamicFormSection";
import { statusOptions } from "../consultationFormOptions";
import WebcamCapture from "@/components/Capture";
// import WebcamCapture from "@/components/Capture";
// import Text from "@/components/text";
// import Button from "@/components/button";

interface SectionFourProps {
  // errorsTemperature: string;
  // errorsBp: string;
  // errorsPulse: string;
  // errorsCvs: string;
  // errorsRs: string;
  // errorsTest: string;
  // postExaminationData: any;
  // mainOnSetHandler: (name: string, value: any) => void;
}

const SectionFive: React.FC<SectionFourProps> = ({
  // errorsTemperature,
  // errorsBp,
  // errorsPulse,
  // errorsCvs,
  // errorsRs,
  // errorsTest,
  // postExaminationData,
  // mainOnSetHandler,
}) => {
  // const examinationDetails = useSelector(
  //   (state: RootState) => state.examinations.examinationDetails
  // );
  // const { values, handleChange } = useForm<Examination | null>(
  //   examinationDetails
  // );
    const { medicineDropdownHandler } = useMedicine();
  

  const consultationDetail = useSelector(
    (state: RootState) =>
      state.consultation.consultationDetailData?.vitals
  );

  // const medicineDropdownData = useSelector(
  //     (state: RootState) => state.medicines.medicineDropdownData
  //   )?.map((item: any) => ({
  //     id: item?.id,
  //     label: item?.medicine_name,
  //     value: item?.medicine_name,
  //   }));

  //   useEffect(() => {
  //       medicineDropdownHandler(() => {});
  //     }, []);



    // const testIds = testData?.split(",")?.map((item: any) => item.trim());
    // const testLabelMap = testObj?.filter((item: any) =>
    //   testIds?.includes(item?.value?.toString())
    // )?.map((item: any) => {
    //   return {
    //     id: item?.value,
    //     label: item?.label,
    //     value: item?.value,
    //   };
    // });
    // const testLabelMap = testObj?.filter((item: any) =>
    //   testIds?.includes(item?.value?.toString())
    // )?.map((item: any) => item?.label)?.join(",");
    // console.log("testLabelMap", testLabelMap);

    

  const { values, handleChange, handleTipTapChange,onSetHandler } =
    useForm<Consultation | null>(consultationDetail);

  return (
    <React.Fragment>
       <WebcamCapture 
          name="consultation_image"
          onChange={(event: any) => {
            onSetHandler("consultation_image", event?.target?.files[0]);
          }}
          accept="image/*,.pdf,.doc,.docx"
          // value={values?.consultation_image}
        />

    </React.Fragment>
  );
};
export default SectionFive;
