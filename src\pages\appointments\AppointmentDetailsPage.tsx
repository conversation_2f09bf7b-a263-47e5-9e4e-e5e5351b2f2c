import AppointmentIndex from ".";
import { useEffect } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { useParams } from "react-router-dom";
import { useAppointments } from "@/actions/calls/appointments";

const AppointmentDetailsPage = () => {
  const params = useParams();

  const { appointmentDetailHandler } = useAppointments();

  const appointmentDetails = useSelector(
    (state: RootState) => state.appointment.appointmentDetailData
  );

  useEffect(() => {
    if (params.id) {
      appointmentDetailHandler(params.id, () => {});
    }
  }, [params.id]);

  return <AppointmentIndex appointmentDetails={appointmentDetails} />;
};

export default AppointmentDetailsPage;
