import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import json
import os
import re
from datetime import datetime

class CodeGeneratorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Custom Code Generator")
        self.root.geometry("1400x900")
        
        # Data storage
        self.boilerplates = {}
        self.current_placeholders = {}
        self.generated_code = ""
        self.boilerplates_file = "boilerplates.json"
        
        # Load saved boilerplates
        self.load_boilerplates()
        
        # Create main UI
        self.create_widgets()
        
    def create_widgets(self):
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Tab 1: Code Generator
        self.generator_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.generator_frame, text="Code Generator")
        self.create_generator_tab()
        
        # Tab 2: Manage Boilerplates
        self.manage_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.manage_frame, text="Manage Boilerplates")
        self.create_manage_tab()
        
    def create_generator_tab(self):
        # Main container with paned window
        main_paned = ttk.PanedWindow(self.generator_frame, orient='horizontal')
        main_paned.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Left panel
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # Right panel
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        # === LEFT PANEL ===
        # Boilerplate selection
        boilerplate_frame = ttk.LabelFrame(left_frame, text="Boilerplate Selection", padding=10)
        boilerplate_frame.pack(fill='x', pady=(0, 10))
        
        ttk.Label(boilerplate_frame, text="Select Boilerplate:").pack(anchor='w')
        self.boilerplate_var = tk.StringVar()
        self.boilerplate_combo = ttk.Combobox(boilerplate_frame, textvariable=self.boilerplate_var, 
                                            values=list(self.boilerplates.keys()), state='readonly')
        self.boilerplate_combo.pack(fill='x', pady=(5, 0))
        self.boilerplate_combo.bind('<<ComboboxSelected>>', self.on_boilerplate_select)
        
        # Code editor
        editor_frame = ttk.LabelFrame(left_frame, text="Code Template", padding=10)
        editor_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # Instructions
        instructions_frame = ttk.Frame(editor_frame)
        instructions_frame.pack(fill='x', pady=(0, 5))
        
        ttk.Label(instructions_frame, 
                 text="💡 Select text and click 'Add Placeholder' or use {{PLACEHOLDER}} format",
                 foreground='blue').pack(side='left')
        
        # Add placeholder button
        self.add_placeholder_btn = ttk.Button(instructions_frame, text="🎯 Add Placeholder", 
                                            command=self.add_placeholder_to_selection)
        self.add_placeholder_btn.pack(side='right')
        
        # Text editor with scrollbar
        editor_container = ttk.Frame(editor_frame)
        editor_container.pack(fill='both', expand=True)
        
        self.code_text = tk.Text(editor_container, wrap='none', font=('Consolas', 10))
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(editor_container, orient='vertical', command=self.code_text.yview)
        h_scrollbar = ttk.Scrollbar(editor_container, orient='horizontal', command=self.code_text.xview)
        
        self.code_text.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout for text and scrollbars
        self.code_text.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        editor_container.grid_rowconfigure(0, weight=1)
        editor_container.grid_columnconfigure(0, weight=1)
        
        # Bind events
        self.code_text.bind('<KeyRelease>', self.on_code_change)
        self.code_text.bind('<ButtonRelease-1>', self.on_text_select)
        
        # Buttons
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill='x')
        
        ttk.Button(button_frame, text="📁 Load from File", command=self.load_code_file).pack(side='left', padx=(0, 5))
        ttk.Button(button_frame, text="💾 Save as Boilerplate", command=self.save_boilerplate).pack(side='left', padx=(0, 5))
        ttk.Button(button_frame, text="🔄 Parse Placeholders", command=self.parse_placeholders).pack(side='left')
        
        # === RIGHT PANEL ===
        # Dynamic form for placeholders
        self.form_frame = ttk.LabelFrame(right_frame, text="Placeholder Values", padding=10)
        self.form_frame.pack(fill='both', expand=True, pady=(0, 10))
        
        # Scrollable frame for form
        self.form_canvas = tk.Canvas(self.form_frame)
        self.form_scrollbar = ttk.Scrollbar(self.form_frame, orient="vertical", command=self.form_canvas.yview)
        self.scrollable_form = ttk.Frame(self.form_canvas)
        
        self.scrollable_form.bind(
            "<Configure>",
            lambda e: self.form_canvas.configure(scrollregion=self.form_canvas.bbox("all"))
        )
        
        self.form_canvas.create_window((0, 0), window=self.scrollable_form, anchor="nw")
        self.form_canvas.configure(yscrollcommand=self.form_scrollbar.set)
        
        self.form_canvas.pack(side="left", fill="both", expand=True)
        self.form_scrollbar.pack(side="right", fill="y")
        
        # Generate button
        generate_frame = ttk.Frame(right_frame)
        generate_frame.pack(fill='x', pady=(0, 10))
        
        self.generate_btn = ttk.Button(generate_frame, text="🚀 Generate Code", command=self.generate_code)
        self.generate_btn.pack(fill='x')
        
        # Preview area
        preview_frame = ttk.LabelFrame(right_frame, text="Generated Code Preview", padding=10)
        preview_frame.pack(fill='both', expand=True)
        
        # Preview text with scrollbar
        preview_container = ttk.Frame(preview_frame)
        preview_container.pack(fill='both', expand=True, pady=(0, 10))
        
        self.preview_text = tk.Text(preview_container, wrap='none', font=('Consolas', 9), 
                                   state='disabled', bg='#f8f8f8')
        
        preview_v_scrollbar = ttk.Scrollbar(preview_container, orient='vertical', command=self.preview_text.yview)
        preview_h_scrollbar = ttk.Scrollbar(preview_container, orient='horizontal', command=self.preview_text.xview)
        
        self.preview_text.configure(yscrollcommand=preview_v_scrollbar.set, xscrollcommand=preview_h_scrollbar.set)
        
        self.preview_text.grid(row=0, column=0, sticky='nsew')
        preview_v_scrollbar.grid(row=0, column=1, sticky='ns')
        preview_h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        preview_container.grid_rowconfigure(0, weight=1)
        preview_container.grid_columnconfigure(0, weight=1)
        
        # Save buttons frame - Make it more visible
        save_frame = ttk.LabelFrame(preview_frame, text="Save Options", padding=10)
        save_frame.pack(fill='x', pady=(5, 0))
        
        # Row 1: Main save buttons
        main_save_frame = ttk.Frame(save_frame)
        main_save_frame.pack(fill='x', pady=(0, 5))
        
        # Primary save button - large and prominent
        self.save_btn = ttk.Button(main_save_frame, text="💾 Save Generated Code", 
                                  command=self.save_generated_code)
        self.save_btn.pack(side='left', padx=(0, 10))
        
        # Quick save button
        self.quick_save_btn = ttk.Button(main_save_frame, text="⚡ Quick Save", 
                                        command=self.quick_save)
        self.quick_save_btn.pack(side='left', padx=(0, 10))
        
        # Row 2: Additional options
        extra_save_frame = ttk.Frame(save_frame)
        extra_save_frame.pack(fill='x')
        
        ttk.Button(extra_save_frame, text="📋 Copy to Clipboard", 
                  command=self.copy_to_clipboard).pack(side='left', padx=(0, 5))
        ttk.Button(extra_save_frame, text="📁 Save As...", 
                  command=self.save_as_dialog).pack(side='left')
        
        # Initially disable save buttons
        self.save_btn.config(state='disabled')
        self.quick_save_btn.config(state='disabled')
        
    def create_manage_tab(self):
        # Boilerplate list
        list_frame = ttk.LabelFrame(self.manage_frame, text="Saved Boilerplates", padding=10)
        list_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Treeview for boilerplates
        columns = ('Name', 'Created', 'Placeholders')
        self.tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        # Define headings
        self.tree.heading('Name', text='Boilerplate Name')
        self.tree.heading('Created', text='Created Date')
        self.tree.heading('Placeholders', text='Placeholders')
        
        # Configure column widths
        self.tree.column('Name', width=200)
        self.tree.column('Created', width=150)
        self.tree.column('Placeholders', width=300)
        
        # Scrollbar for treeview
        tree_scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.tree.yview)
        self.tree.configure(yscrollcommand=tree_scrollbar.set)
        
        self.tree.pack(side='left', fill='both', expand=True)
        tree_scrollbar.pack(side='right', fill='y')
        
        # Buttons
        button_frame = ttk.Frame(self.manage_frame)
        button_frame.pack(fill='x', padx=10, pady=(0, 10))
        
        ttk.Button(button_frame, text="✏️ Edit", command=self.edit_boilerplate).pack(side='left', padx=(0, 5))
        ttk.Button(button_frame, text="🗑️ Delete", command=self.delete_boilerplate).pack(side='left', padx=(0, 5))
        ttk.Button(button_frame, text="🔄 Refresh", command=self.refresh_boilerplate_list).pack(side='left')
        
        # Populate the tree
        self.refresh_boilerplate_list()
        
    def on_text_select(self, event=None):
        """Handle text selection in code editor"""
        try:
            selected_text = self.code_text.selection_get()
            if selected_text:
                self.add_placeholder_btn.config(state='normal')
            else:
                self.add_placeholder_btn.config(state='disabled')
        except tk.TclError:
            # No selection
            self.add_placeholder_btn.config(state='disabled')
    
    def add_placeholder_to_selection(self):
        """Add placeholder brackets to selected text"""
        try:
            # Get selected text
            selected_text = self.code_text.selection_get()
            if not selected_text:
                messagebox.showwarning("Warning", "Please select some text first!")
                return
            
            # Ask for placeholder name
            placeholder_name = simpledialog.askstring(
                "Placeholder Name", 
                f"Enter placeholder name for: '{selected_text[:50]}{'...' if len(selected_text) > 50 else ''}'",
                initialvalue=selected_text.upper().replace(' ', '_')
            )
            
            if not placeholder_name:
                return
            
            # Clean placeholder name
            placeholder_name = re.sub(r'[^A-Za-z0-9_]', '_', placeholder_name).upper()
            
            # Get selection indices
            start_idx = self.code_text.index(tk.SEL_FIRST)
            end_idx = self.code_text.index(tk.SEL_LAST)
            
            # Replace selected text with placeholder
            placeholder_text = f"{{{{{placeholder_name}}}}}"
            self.code_text.delete(start_idx, end_idx)
            self.code_text.insert(start_idx, placeholder_text)
            
            # Parse placeholders to update form
            self.parse_placeholders()
            
            messagebox.showinfo("Success", f"Placeholder '{placeholder_name}' added!")
            
        except tk.TclError:
            messagebox.showwarning("Warning", "Please select some text first!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to add placeholder: {str(e)}")
        
    def load_boilerplates(self):
        """Load boilerplates from JSON file"""
        try:
            if os.path.exists(self.boilerplates_file):
                with open(self.boilerplates_file, 'r', encoding='utf-8') as f:
                    self.boilerplates = json.load(f)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load boilerplates: {str(e)}")
            
    def save_boilerplates(self):
        """Save boilerplates to JSON file"""
        try:
            with open(self.boilerplates_file, 'w', encoding='utf-8') as f:
                json.dump(self.boilerplates, f, indent=2, ensure_ascii=False)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save boilerplates: {str(e)}")
            
    def on_boilerplate_select(self, event=None):
        """Handle boilerplate selection"""
        selected = self.boilerplate_var.get()
        if selected and selected in self.boilerplates:
            self.code_text.delete(1.0, tk.END)
            self.code_text.insert(1.0, self.boilerplates[selected]['code'])
            self.parse_placeholders()
            
    def on_code_change(self, event=None):
        """Handle code text changes"""
        # Auto-parse placeholders when code changes
        self.root.after(500, self.parse_placeholders)  # Debounce
        
    def parse_placeholders(self):
        """Parse placeholders from code and create form"""
        code = self.code_text.get(1.0, tk.END)
        
        # Find all placeholders using regex
        placeholders = re.findall(r'\{\{([^}]+)\}\}', code)
        unique_placeholders = list(set(placeholders))
        
        # Clear existing form
        for widget in self.scrollable_form.winfo_children():
            widget.destroy()
            
        self.current_placeholders = {}
        
        if not unique_placeholders:
            ttk.Label(self.scrollable_form, text="No placeholders found.\nUse {{PLACEHOLDER}} format or select text and click 'Add Placeholder'.",
                     foreground='gray').pack(pady=20)
            return
            
        # Create form fields
        for i, placeholder in enumerate(unique_placeholders):
            frame = ttk.Frame(self.scrollable_form)
            frame.pack(fill='x', padx=5, pady=5)
            
            # Label
            ttk.Label(frame, text=f"{{{{%s}}}}" % placeholder, font=('Consolas', 9)).pack(anchor='w')
            
            # Entry
            var = tk.StringVar()
            entry = ttk.Entry(frame, textvariable=var, font=('Consolas', 9))
            entry.pack(fill='x', pady=(2, 0))
            
            # Store reference
            self.current_placeholders[placeholder] = var
            
            # Bind enter key to generate
            entry.bind('<Return>', lambda e: self.generate_code())
            
        # Update scroll region
        self.scrollable_form.update_idletasks()
        self.form_canvas.configure(scrollregion=self.form_canvas.bbox("all"))
        
    def load_code_file(self):
        """Load code from file"""
        file_path = filedialog.askopenfilename(
            title="Select Code File",
            filetypes=[
                ("JavaScript files", "*.js"),
                ("TypeScript files", "*.ts"),
                ("Python files", "*.py"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.code_text.delete(1.0, tk.END)
                    self.code_text.insert(1.0, content)
                    self.parse_placeholders()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load file: {str(e)}")
                
    def save_boilerplate(self):
        """Save current code as boilerplate"""
        code = self.code_text.get(1.0, tk.END).strip()
        if not code:
            messagebox.showwarning("Warning", "No code to save!")
            return
            
        # Get boilerplate name
        name = simpledialog.askstring("Save Boilerplate", "Enter boilerplate name:")
        if not name:
            return
            
        # Extract placeholders
        placeholders = re.findall(r'\{\{([^}]+)\}\}', code)
        unique_placeholders = list(set(placeholders))
        
        # Save boilerplate
        self.boilerplates[name] = {
            'code': code,
            'created': datetime.now().isoformat(),
            'placeholders': unique_placeholders
        }
        
        self.save_boilerplates()
        
        # Update combo box
        self.boilerplate_combo['values'] = list(self.boilerplates.keys())
        self.boilerplate_var.set(name)
        
        # Refresh manage tab
        self.refresh_boilerplate_list()
        
        messagebox.showinfo("Success", f"Boilerplate '{name}' saved successfully!")
        
    def generate_code(self):
        """Generate code by replacing placeholders"""
        code = self.code_text.get(1.0, tk.END).strip()
        if not code:
            messagebox.showwarning("Warning", "No code template provided!")
            return
            
        if not self.current_placeholders:
            messagebox.showwarning("Warning", "No placeholders found!")
            return
            
        # Replace placeholders
        generated = code
        for placeholder, var in self.current_placeholders.items():
            value = var.get().strip()
            if not value:
                messagebox.showwarning("Warning", f"Please provide a value for {{{{%s}}}}" % placeholder)
                return
                
            generated = generated.replace(f"{{{{{placeholder}}}}}", value)
            
        self.generated_code = generated
        
        # Update preview
        self.preview_text.config(state='normal')
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(1.0, generated)
        self.preview_text.config(state='disabled')
        
        # Enable save buttons
        self.save_btn.config(state='normal')
        self.quick_save_btn.config(state='normal')
        
        messagebox.showinfo("Success", "Code generated successfully!\nYou can now save it using the buttons below.")
        
    def save_generated_code(self):
        """Save generated code to file with options"""
        if not self.generated_code:
            messagebox.showwarning("Warning", "No generated code to save!")
            return
            
        # Ask user for save preference
        choice = messagebox.askyesnocancel("Save Options", 
                                         "Choose save option:\n\n" +
                                         "YES - Quick save with auto-generated filename\n" +
                                         "NO - Choose custom filename and location\n" +
                                         "CANCEL - Don't save")
        
        if choice is None:  # Cancel
            return
        elif choice:  # Yes - Quick save
            self.quick_save()
        else:  # No - Custom save
            self.save_as_dialog()
            
    def quick_save(self):
        """Quick save with auto-generated filename"""
        if not self.generated_code:
            messagebox.showwarning("Warning", "No generated code to save!")
            return
            
        try:
            # Generate filename based on current timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"generated_code_{timestamp}.js"
            
            # Create 'generated' directory if it doesn't exist
            if not os.path.exists('generated'):
                os.makedirs('generated')
                
            file_path = os.path.join('generated', filename)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(self.generated_code)
                
            messagebox.showinfo("Success", f"Code saved to:\n{file_path}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save file: {str(e)}")
            
    def save_as_dialog(self):
        """Save generated code with custom filename and location"""
        if not self.generated_code:
            messagebox.showwarning("Warning", "No generated code to save!")
            return
            
        file_path = filedialog.asksaveasfilename(
            title="Save Generated Code",
            defaultextension=".js",
            filetypes=[
                ("JavaScript files", "*.js"),
                ("TypeScript files", "*.ts"),
                ("Python files", "*.py"),
                ("React Component", "*.jsx"),
                ("React TypeScript", "*.tsx"),
                ("HTML files", "*.html"),
                ("CSS files", "*.css"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.generated_code)
                messagebox.showinfo("Success", f"Code saved to:\n{file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save file: {str(e)}")
                
    def copy_to_clipboard(self):
        """Copy generated code to clipboard"""
        if not self.generated_code:
            messagebox.showwarning("Warning", "No generated code to copy!")
            return
            
        self.root.clipboard_clear()
        self.root.clipboard_append(self.generated_code)
        messagebox.showinfo("Success", "Code copied to clipboard!")
        
    def refresh_boilerplate_list(self):
        """Refresh the boilerplate list in manage tab"""
        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)
            
        # Add boilerplates
        for name, data in self.boilerplates.items():
            created = data.get('created', 'Unknown')
            if created != 'Unknown':
                try:
                    created_date = datetime.fromisoformat(created)
                    created = created_date.strftime('%Y-%m-%d %H:%M')
                except:
                    pass
                    
            placeholders = ', '.join(data.get('placeholders', []))
            self.tree.insert('', 'end', values=(name, created, placeholders))
            
    def edit_boilerplate(self):
        """Edit selected boilerplate"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a boilerplate to edit!")
            return
            
        item = self.tree.item(selected[0])
        name = item['values'][0]
        
        if name in self.boilerplates:
            # Switch to generator tab and load the boilerplate
            self.notebook.select(0)  # Select first tab
            self.boilerplate_var.set(name)
            self.on_boilerplate_select()
            
    def delete_boilerplate(self):
        """Delete selected boilerplate"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("Warning", "Please select a boilerplate to delete!")
            return
            
        item = self.tree.item(selected[0])
        name = item['values'][0]
        
        if messagebox.askyesno("Confirm Delete", f"Are you sure you want to delete '{name}'?"):
            if name in self.boilerplates:
                del self.boilerplates[name]
                self.save_boilerplates()
                self.refresh_boilerplate_list()
                
                # Update combo box
                self.boilerplate_combo['values'] = list(self.boilerplates.keys())
                if self.boilerplate_var.get() == name:
                    self.boilerplate_var.set('')
                    
                messagebox.showinfo("Success", f"Boilerplate '{name}' deleted successfully!")

def main():
    root = tk.Tk()
    app = CodeGeneratorApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()