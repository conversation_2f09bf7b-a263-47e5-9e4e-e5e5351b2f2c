const fs = require("fs");
const path = require("path");
const readline = require("readline");

// inputs to take from user to create a boilder plate.
/* 
1. File Name
2. interface name
3. slice name (optional)
*/
 const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

rl.question("Enter the file name: ", (fileName) => {
  rl.question("Enter the state interface name and path: ", (interfaceDetails) => {
    rl.question("Enter the slice name: ", (sliceName) => {
      rl.close();
      createFile(fileName, interfaceDetails, sliceName);
    });
  });
});

 const createFile = (fileName, interfaceDetails, sliceName) => {
  const dirPath = path.join(__dirname, "..","src", "actions", "slices");
  const filePath = path.join(dirPath,  fileName + ".ts");
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath);
  }
  const pascalCasedFileName = fileName
  .replace(/_/g, " ")
  .replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.slice(1).toLowerCase())
  .replace(/\s+/g, "");
  const camelCasedFileName = fileName.replace(/[_\s]+(.)?/g, (_, c) => (c ? c.toUpperCase() : ""))
    .replace(/^(.)/, (m) => m.toLowerCase());

  const [interfaceName, interfacePath] = interfaceDetails.split(",");
  const fileContent = `
   import { ${interfaceName}  } from "${interfacePath}";
import { createSlice } from "@reduxjs/toolkit";

const initialState: ${interfaceName} = {
  ${camelCasedFileName}DetailData: {},
  ${camelCasedFileName}ListData: [],
  ${camelCasedFileName}DropdownData: [],
};

const ${camelCasedFileName}Slice = createSlice({
  name: "${sliceName || camelCasedFileName}",
  initialState,
  reducers: {
    ${camelCasedFileName}DetailSlice: (state, action) => {
      state.${camelCasedFileName}DetailData = action?.payload;
    },
    ${camelCasedFileName}ListSlice: (state, action) => {
      state.${camelCasedFileName}ListData = action?.payload;
    },
    ${camelCasedFileName}DropdownSlice: (state, action) => {
        state.${camelCasedFileName}DropdownData = action?.payload;
    },
    clear${camelCasedFileName.charAt(0).toUpperCase() + camelCasedFileName.slice(1)}DetailSlice: (state) => {
      state.${camelCasedFileName}DetailData = null;
    },
  },
});

export default ${camelCasedFileName}Slice.reducer;

export const {
  ${camelCasedFileName}DetailSlice,
  ${camelCasedFileName}ListSlice,
  clear${camelCasedFileName.charAt(0).toUpperCase() + camelCasedFileName.slice(1)}DetailSlice,
  ${camelCasedFileName}DropdownSlice,
} = ${camelCasedFileName}Slice.actions;


  `;
  fs.writeFileSync(filePath, fileContent);
  console.log(`File created at ${filePath}`);
};