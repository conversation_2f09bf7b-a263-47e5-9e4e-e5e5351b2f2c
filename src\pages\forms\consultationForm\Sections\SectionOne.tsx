import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>H<PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import Input from "@/components/input";
import View from "@/components/view";
import Text from "@/components/text";
import Switch from "@/components/ui/switch";
import useForm from "@/utils/custom-hooks/use-form";
import { Consultation } from "@/interfaces/consultation";
import InfoCard from "@/components/ui/infoCard";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { Calendar, User } from "lucide-react";
import SingleSelector from "@/components/SingleSelector";

const SectionOne: React.FC = () => {
  const consultationDetail = useSelector(
    (state: RootState) =>
      state.consultation.consultationDetailData?.consultations
  );
  const { values, handleChange, handleTipTapChange, onSetHandler } =
    useForm<Consultation | null>(consultationDetail);


  return (
    <>
      <Card className="mt-2">   
        {/* <CardHeader>
          <CardTitle>Patient & Appointment Details</CardTitle>
        </CardHeader> */}
        {/* <CardContent className="space-y-4"> */}
          <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <InfoCard
              label="Patient Name"
              value={values?.patient_id || "N/A"}
              icon={<User className="h-5 w-5 text-primary" />}
            />
            <InfoCard
              label="Appointment Number"
              value={values?.appointment_id || "N/A"}
              icon={<Calendar className="h-5 w-5 text-primary" />}
            />
            <InfoCard
              label="Doctor Name"
              value={values?.doctor_id+ ""  || "N/A"}
              icon={<User className="h-5 w-5 text-primary" />}
            />
          </View>

          <View className="grid grid-cols-1 md:grid-cols-2  gap-4 mt-6">
            {/* appointment type */}
            <SingleSelector
              id="type"
              label="Appointment Type"
              name="type"
              // error={errorsType}
              value={values?.type || ""}
              placeholder="Select Appointment Type"
              onChange={(value) => {
                onSetHandler("type", value);
              }}
              options={[
                { label: "First Visit", value: "First Visit" },
                { label: "Follow Up", value: "Follow Up" },
              ]}
              required={true}
            />
            <View>
              <Input
                id="referred_by_name"
                name="referred_by_name"
                label="Referred By Name"
                onChange={handleChange}
                // error={errorsReferredByName}
                value={values?.referred_by_name ?? ""}
                placeholder="Referred By Name"
              />
            </View>
          </View>
        {/* </CardContent> */}
      </Card>
    </>
  )
}

export default SectionOne;
        