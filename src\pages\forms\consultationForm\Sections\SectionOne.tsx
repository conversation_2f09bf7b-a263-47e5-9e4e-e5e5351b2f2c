import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>H<PERSON>er, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import Input from "@/components/input";
import View from "@/components/view";
import Text from "@/components/text";
import Switch from "@/components/ui/switch";
import useForm from "@/utils/custom-hooks/use-form";
import { Consultation } from "@/interfaces/consultation";
import InfoCard from "@/components/ui/infoCard";
import { useSelector } from "react-redux";
import { RootState } from "@/actions/store";
import { Calendar, Hash, Mail, Phone, Stethoscope, User } from "lucide-react";
import SingleSelector from "@/components/SingleSelector";
import { appointmentTypeOptions } from "../consultationFormOptions";

const SectionOne: React.FC = () => {
  const consultationDetail = useSelector(
    (state: RootState) =>
      state.consultation.consultationDetailData?.consultations
  );
  const { values, handleChange, handleTipTapChange, onSetH<PERSON><PERSON> } =
    useForm<Consultation | null>(consultationDetail);


  return (
    <>
      <Card className="mt-2">   
        {/* <CardHeader>
          <CardTitle>Patient & Appointment Details</CardTitle>
        </CardHeader> */}
        {/* <CardContent className="space-y-4"> */}
          {/* <View className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <InfoCard
              label="Patient Name"
              value={values?.patient_id || "N/A"}
              icon={<User className="h-5 w-5 text-primary" />}
            />
            <InfoCard
              label="Appointment Number"
              value={values?.appointment_id || "N/A"}
              icon={<Calendar className="h-5 w-5 text-primary" />}
            />
            <InfoCard
              label="Doctor Name"
              value={values?.doctor_id+ ""  || "N/A"}
              icon={<User className="h-5 w-5 text-primary" />}
            />
          </View> */}
          <div className="space-y-4">
      {/* Patient Information Card */}
      <Card className="!bg-background">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <User className="h-5 w-5" />
            Patient Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              {/* <div>
                <div className="flex items-center gap-2 mb-1">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Patient ID</span>
                </div>
                <p className="text-sm text-muted-foreground font-mono">{consultationDetail?.patient_id}</p>
              </div> */}
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Patient Number</span>
                </div>
                <p className="text-sm text-muted-foreground">{consultationDetail?.patient_number}</p>
              </div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Name</span>
                </div>
                <p className="text-sm font-semibold">{consultationDetail?.patient_name}</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Email</span>
                </div>
                <p className="text-sm text-muted-foreground">{consultationDetail?.patient_email}</p>
              </div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Phone</span>
                </div>
                <p className="text-sm text-muted-foreground">{consultationDetail?.patient_phone}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Appointment Information Card */}
      <Card className="!bg-background">
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Calendar className="h-5 w-5" />
            Appointment Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* <div>
              <div className="flex items-center gap-2 mb-1">
                <Hash className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Appointment ID</span>
              </div>
              <p className="text-sm text-muted-foreground font-mono">{consultationDetail?.appointment_id}</p>
            </div> */}
            <div>
              <div className="flex items-center gap-2 mb-1">
                <Hash className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Appointment Number</span>
              </div>
              <p className="text-sm text-muted-foreground">{consultationDetail?.appointment_number}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Doctor Information Card */}
      {/* <Card>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Stethoscope className="h-5 w-5" />
            Doctor Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Doctor ID</span>
                </div>
                <p className="text-sm text-muted-foreground font-mono">{consultationDetail?.doctor_id}</p>
              </div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Stethoscope className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Doctor Name</span>
                </div>
                <p className="text-sm font-semibold">{consultationDetail?.doctor_name}</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Email</span>
                </div>
                <p className="text-sm text-muted-foreground">{consultationDetail?.doctor_email}</p>
              </div>
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Phone</span>
                </div>
                <p className="text-sm text-muted-foreground">{consultationDetail?.doctor_phone}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card> */}
    </div>

          <View className="grid grid-cols-1 md:grid-cols-2  gap-4 mt-6">
            {/* appointment type */}
            <View className={`${values?.appointment_type === "First Visit" ? "" : "col-span-2"}`}>
              <SingleSelector
              id="appointment_type"
              label="Appointment Type"
              name="appointment_type"
              // error={errorsType}
              value={values?.appointment_type || ""}
              placeholder="Select Appointment Type"
              onChange={(value) => {
                onSetHandler("appointment_type", value);
              }}
              options={appointmentTypeOptions}
              required={true}
            />
            </View>
            {
              values?.appointment_type === "First Visit" ? (
                <View>
              <Input
                id="referred_by_name"
                name="referred_by_name"
                label="Referred By Name"
                onChange={handleChange}
                // error={errorsReferredByName}
                value={values?.referred_by_name ?? ""}
                placeholder="Referred By Name"
              />
            </View>
              ) : null
            }
          </View>
        {/* </CardContent> */}
      </Card>
    </>
  )
}

export default SectionOne;
        