import { Gender, MaratalStatus } from "../index";

export enum Role {
  ADMIN = "Admin",
  DOCTOR = "Doctor",
  NURSE = "Nurse",
  RECEPTIONIST = "Receptionist",
  PHARMACIST = "Pharmacist",
  LABORATORIST = "Laboratorist",
  ACCOUNTANT = "Accountant",
  PATIENT = "patient",
  //   USER = "User",
}

export interface UserInterface {
  //    readonly userId: string,
  name: string;
  email: string;
  phone: string;
  // readonly password: string;
  // image: File | string;
  age: number;
  DOB: Date;
  gender: Gender;
  address: string;
  country: string | "India";
  state: string;
  city: string;
  pincode: string;
  // marital_status: MaratalStatus;
  marital_status?: [];
  id_type?: string;
  id_value?: string;
  id_number_masked?: string;
  id_edited?: boolean;
  consent: boolean;
  Adhar?: string;
  Passport?: string;
  Voter_ID?: string;
  Driving_License?: string;
  Ration_Card?: string;
  // idValue: string;
  department: string;
  designation: string;
  qualification: string;
  // department: string;
  role: Role;
  image?: File[] | string[] | null;
  // existing_files?: string[] | null;

  // status: boolean;
}
